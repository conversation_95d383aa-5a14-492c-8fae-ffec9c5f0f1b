"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupRedisKeyEvents = setupRedisKeyEvents;
const redis_1 = require("./redis");
const queue_1 = require("./queue");
function setupRedisKeyEvents(redisSubscriber) {
    redisSubscriber.on('message', (channel, message) => {
        // channel will be like '__keyevent@0__:expired'
        // message will be the key that expired, e.g., 'session-expiry:someSessionId'
        console.log(`Redis Key Event: Channel: ${channel}, Message: ${message}`);
        if (channel === `__keyevent@0__:expired`) {
            // Handle expired sessions from SESSION_EXPIRY
            if (message.startsWith(`${redis_1.REDIS_KEYS.SESSIONS.EXPIRY}:`)) {
                const sessionId = message.split(':')[2]; // gomama:sessions:expiry:sessionId
                console.log(`Session ${sessionId} expired. Adding to cleanup queue.`);
                queue_1.QueueManager.addSessionCleanup({ sessionId, reason: 'expired' });
                // Optionally, send feedback notification here if it's tied to expiry
                // You'll need to fetch userId from Redis using sessionId
                // Example:
                // redisClient.hget(REDIS_KEYS.SESSIONS.BY_USER, sessionId).then(userId => {
                //   if (userId) sendFeedbackNotification(sessionId, userId);
                // });
            }
            // Handle idle sessions from SESSION_ENTRY_CHECK
            if (message.startsWith(`${redis_1.REDIS_KEYS.SESSIONS.ENTRY_CHECK}:`)) {
                const sessionId = message.split(':')[2]; // gomama:sessions:entry_check:sessionId
                console.log(`Session ${sessionId} idle entry check expired. Adding to cleanup queue.`);
                // This might need a different job type or logic if it's not a full cleanup
                // For now, assuming it also leads to a cleanup
                queue_1.QueueManager.addSessionCleanup({ sessionId, reason: 'idle' });
                // Optionally, send feedback notification here if it's tied to idle expiry
                // You'll need to fetch userId from Redis using sessionId
                // Example:
                // redisClient.hget(REDIS_KEYS.SESSIONS.BY_USER, sessionId).then(userId => {
                //   if (userId) sendFeedbackNotification(sessionId, userId);
                // });
            }
        }
    });
    // Subscribe to keyspace events for expired keys
    redisSubscriber.subscribe(`__keyevent@0__:expired`);
    console.log('Subscribed to Redis keyspace events for expired keys.');
}
