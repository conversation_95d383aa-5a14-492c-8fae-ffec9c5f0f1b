"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSystemMetrics = getSystemMetrics;
exports.setupMonitoring = setupMonitoring;
const os_1 = __importDefault(require("os"));
// Keep a rolling window of CPU metrics
const CPU_METRICS_WINDOW_SIZE = 60; // Keep 60 data points
const cpuMetrics = [];
// Previous CPU times for calculating usage
let prevCpuInfo = null;
/**
 * Calculate and record CPU usage
 */
function recordCpuUsage() {
    const cpus = os_1.default.cpus();
    // Sum up all CPU times across all cores
    let idle = 0;
    let total = 0;
    for (const cpu of cpus) {
        idle += cpu.times.idle;
        total += cpu.times.user + cpu.times.nice + cpu.times.sys + cpu.times.idle + cpu.times.irq;
    }
    if (prevCpuInfo) {
        // Calculate CPU usage as a percentage
        const idleDiff = idle - prevCpuInfo.idle;
        const totalDiff = total - prevCpuInfo.total;
        const usage = 100 - Math.floor((100 * idleDiff) / totalDiff);
        // Add to metrics
        cpuMetrics.push({
            timestamp: Date.now(),
            usage
        });
        // Keep only the last N metrics
        if (cpuMetrics.length > CPU_METRICS_WINDOW_SIZE) {
            cpuMetrics.shift();
        }
        // Log high CPU usage
        if (usage > 80) {
            console.warn(`⚠️ High CPU usage detected: ${usage}%`);
        }
    }
    // Update previous CPU info
    prevCpuInfo = { idle, total };
}
/**
 * Get memory usage statistics
 */
function getMemoryUsage() {
    const totalMem = os_1.default.totalmem();
    const freeMem = os_1.default.freemem();
    const usedMem = totalMem - freeMem;
    const memUsagePercent = Math.floor((usedMem / totalMem) * 100);
    // Log high memory usage
    if (memUsagePercent > 80) {
        console.warn(`⚠️ High memory usage detected: ${memUsagePercent}%`);
    }
    return {
        total: formatBytes(totalMem),
        free: formatBytes(freeMem),
        used: formatBytes(usedMem),
        percentUsed: memUsagePercent
    };
}
/**
 * Format bytes to a human-readable format
 */
function formatBytes(bytes) {
    if (bytes === 0)
        return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
/**
 * Get current system metrics
 */
function getSystemMetrics() {
    return {
        cpu: {
            currentUsage: cpuMetrics.length > 0 ? cpuMetrics[cpuMetrics.length - 1].usage : 0,
            averageUsage: cpuMetrics.length > 0
                ? Math.floor(cpuMetrics.reduce((sum, metric) => sum + metric.usage, 0) / cpuMetrics.length)
                : 0,
            history: cpuMetrics
        },
        memory: getMemoryUsage(),
        uptime: formatUptime(os_1.default.uptime()),
        hostname: os_1.default.hostname(),
        platform: os_1.default.platform(),
        loadAverage: os_1.default.loadavg()
    };
}
/**
 * Format uptime to a human-readable format
 */
function formatUptime(uptime) {
    const days = Math.floor(uptime / (60 * 60 * 24));
    const hours = Math.floor((uptime % (60 * 60 * 24)) / (60 * 60));
    const minutes = Math.floor((uptime % (60 * 60)) / 60);
    const seconds = Math.floor(uptime % 60);
    return `${days}d ${hours}h ${minutes}m ${seconds}s`;
}
/**
 * Setup monitoring interval
 */
function setupMonitoring() {
    console.log('Setting up system monitoring...');
    // Record CPU usage immediately
    recordCpuUsage();
    // Then record every 5 seconds
    setInterval(() => {
        recordCpuUsage();
    }, 5000);
    // Log system metrics every minute
    setInterval(() => {
        const metrics = getSystemMetrics();
        console.log('=== System Metrics ===');
        console.log(`CPU Usage: ${metrics.cpu.currentUsage}% (avg: ${metrics.cpu.averageUsage}%)`);
        console.log(`Memory: ${metrics.memory.used} / ${metrics.memory.total} (${metrics.memory.percentUsed}%)`);
        console.log(`Load Average: ${metrics.loadAverage.join(', ')}`);
        console.log(`Uptime: ${metrics.uptime}`);
        console.log('=====================');
    }, 60000);
    return {
        getSystemMetrics
    };
}
