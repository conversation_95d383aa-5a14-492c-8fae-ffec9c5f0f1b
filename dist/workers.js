"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.workers = exports.systemTasksWorker = exports.notificationWorker = exports.sessionCleanupWorker = void 0;
const bullmq_1 = require("bullmq");
const messaging_1 = require("firebase-admin/messaging");
const redis_1 = require("./redis");
// Session cleanup worker
exports.sessionCleanupWorker = new bullmq_1.Worker('gomama:session-cleanup', async (job) => {
    const { sessionId, userId, reason } = job.data;
    console.log(`Processing session cleanup for ${sessionId}, reason: ${reason}`);
    try {
        // Get session data before cleanup
        const sessionData = await redis_1.redisClient.hget(redis_1.REDIS_KEYS.SESSIONS.ACTIVE, sessionId);
        if (!sessionData) {
            console.log(`Session ${sessionId} not found, skipping cleanup`);
            return;
        }
        const session = JSON.parse(sessionData);
        const actualUserId = userId || session.user_id;
        // Pipeline cleanup operations
        const pipeline = redis_1.redisClient.pipeline();
        // Remove session from active sessions
        pipeline.hdel(redis_1.REDIS_KEYS.SESSIONS.ACTIVE, sessionId);
        // Remove user session mapping
        if (actualUserId) {
            pipeline.hdel(redis_1.REDIS_KEYS.SESSIONS.BY_USER, actualUserId);
        }
        // Remove from expiry tracking
        pipeline.zrem(redis_1.REDIS_KEYS.SESSIONS.EXPIRY, sessionId);
        pipeline.zrem(redis_1.REDIS_KEYS.SESSIONS.ENTRY_CHECK, sessionId);
        // Execute cleanup
        await pipeline.exec();
        console.log(`Successfully cleaned up session ${sessionId}`);
        // Publish cleanup notification to AdonisJS
        await redis_1.redisClient.publish('gomama:session:cleanup', JSON.stringify({
            sessionId,
            userId: actualUserId,
            reason,
            timestamp: Date.now()
        }));
    }
    catch (error) {
        console.error(`Error cleaning up session ${sessionId}:`, error);
        throw error; // This will mark the job as failed and trigger retry
    }
}, { connection: redis_1.redisClient });
// Notification worker
exports.notificationWorker = new bullmq_1.Worker('gomama:notifications', async (job) => {
    const { type, userId, message, devices } = job.data;
    console.log(`Processing ${type} notification for user ${userId}`);
    try {
        if (!devices || devices.length === 0) {
            console.log(`No devices found for user ${userId}, skipping notification`);
            return;
        }
        const messaging = (0, messaging_1.getMessaging)();
        const multicastMessage = {
            notification: {
                title: message.title,
                body: message.body
            },
            data: message.data || {},
            tokens: devices
        };
        const response = await messaging.sendEachForMulticast(multicastMessage);
        console.log(`Notification sent: ${response.successCount} successful, ${response.failureCount} failed`);
        // Log failed tokens for debugging
        if (response.failureCount > 0) {
            response.responses.forEach((resp, idx) => {
                if (!resp.success) {
                    console.error(`Failed to send to token ${devices[idx]}:`, resp.error);
                }
            });
        }
    }
    catch (error) {
        console.error(`Error sending ${type} notification to user ${userId}:`, error);
        throw error;
    }
}, { connection: redis_1.redisClient });
// System tasks worker
exports.systemTasksWorker = new bullmq_1.Worker('gomama:system-tasks', async (job) => {
    const { task, params } = job.data;
    console.log(`Processing system task: ${task}`);
    try {
        switch (task) {
            case 'cleanup_expired_keys':
                await cleanupExpiredKeys();
                break;
            case 'update_metrics':
                await updateSystemMetrics();
                break;
            case 'health_check':
                await performHealthCheck();
                break;
            default:
                throw new Error(`Unknown system task: ${task}`);
        }
        console.log(`System task ${task} completed successfully`);
    }
    catch (error) {
        console.error(`Error processing system task ${task}:`, error);
        throw error;
    }
}, { connection: redis_1.redisClient });
// System task implementations
async function cleanupExpiredKeys() {
    // Clean up any orphaned keys or expired data
    const expiredSessions = await redis_1.redisClient.zrangebyscore(redis_1.REDIS_KEYS.SESSIONS.EXPIRY, 0, Date.now());
    if (expiredSessions.length > 0) {
        console.log(`Found ${expiredSessions.length} expired sessions to cleanup`);
        // These should be handled by Redis keyspace events, but this is a backup
    }
}
async function updateSystemMetrics() {
    // Update system metrics in Redis
    const metrics = {
        timestamp: Date.now(),
        memory: process.memoryUsage(),
        uptime: process.uptime()
    };
    await redis_1.redisClient.hset(redis_1.REDIS_KEYS.SYSTEM.METRICS, 'current', JSON.stringify(metrics));
}
async function performHealthCheck() {
    // Perform basic health checks
    const checks = {
        redis: await checkRedisHealth(),
        queues: await checkQueueHealth(),
        timestamp: Date.now()
    };
    await redis_1.redisClient.hset(redis_1.REDIS_KEYS.SYSTEM.HEALTH, 'status', JSON.stringify(checks));
}
async function checkRedisHealth() {
    try {
        await redis_1.redisClient.ping();
        return true;
    }
    catch (_a) {
        return false;
    }
}
async function checkQueueHealth() {
    // This would check queue health - simplified for now
    return { status: 'healthy' };
}
// Error handlers for workers
exports.sessionCleanupWorker.on('failed', (job, err) => {
    console.error(`Session cleanup job ${job === null || job === void 0 ? void 0 : job.id} failed:`, err);
});
exports.notificationWorker.on('failed', (job, err) => {
    console.error(`Notification job ${job === null || job === void 0 ? void 0 : job.id} failed:`, err);
});
exports.systemTasksWorker.on('failed', (job, err) => {
    console.error(`System task job ${job === null || job === void 0 ? void 0 : job.id} failed:`, err);
});
// Success handlers
exports.sessionCleanupWorker.on('completed', (job) => {
    console.log(`Session cleanup job ${job.id} completed`);
});
exports.notificationWorker.on('completed', (job) => {
    console.log(`Notification job ${job.id} completed`);
});
exports.systemTasksWorker.on('completed', (job) => {
    console.log(`System task job ${job.id} completed`);
});
exports.workers = {
    sessionCleanup: exports.sessionCleanupWorker,
    notifications: exports.notificationWorker,
    systemTasks: exports.systemTasksWorker
};
