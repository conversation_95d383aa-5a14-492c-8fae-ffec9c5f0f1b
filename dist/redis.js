"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.redisClient = exports.RedisKeyBuilder = exports.REDIS_KEYS = void 0;
exports.setupRedis = setupRedis;
exports.scanAllKeys = scanAllKeys;
const ioredis_1 = __importDefault(require("ioredis"));
// Improved Redis key structure with consistent naming and clear organization
exports.REDIS_KEYS = {
    // Listings - Hash structure for efficient lookups
    LISTINGS: {
        ACTIVE: 'gomama:listings:active', // Hash: {listing_id: status}
        STATUS_HISTORY: 'gomama:listings:history' // Hash: {listing_id: json_history}
    },
    // Sessions - Organized by data type
    SESSIONS: {
        ACTIVE: 'gomama:sessions:active', // Hash: {session_id: json_data}
        BY_USER: 'gomama:sessions:by_user', // Hash: {user_id: session_id}
        EXPIRY: 'gomama:sessions:expiry', // Sorted Set: {session_id: expiry_timestamp}
        ENTRY_CHECK: 'gomama:sessions:entry_check' // Sorted Set: {session_id: deadline_timestamp}
    },
    // Users & Devices
    USERS: {
        DEVICES: 'gomama:users:devices', // Hash: {user_id: json_device_array}
        SESSIONS: 'gomama:users:sessions' // Hash: {user_id: current_session_id}
    },
    // Pub/Sub channels for real-time updates
    CHANNELS: {
        LISTING_STATUS: 'gomama:channels:listing_status', // Pattern: listing_status:{listing_id}
        SESSION_UPDATE: 'gomama:channels:session_update', // Pattern: session_update:{session_id}
        USER_NOTIFICATION: 'gomama:channels:user_notification' // Pattern: user_notification:{user_id}
    },
    // System keys
    SYSTEM: {
        METRICS: 'gomama:system:metrics',
        HEALTH: 'gomama:system:health'
    }
};
// Helper functions for key construction
exports.RedisKeyBuilder = {
    // Listing keys
    listingStatus: (listingId) => `${exports.REDIS_KEYS.CHANNELS.LISTING_STATUS}:${listingId}`,
    // Session keys
    sessionUpdate: (sessionId) => `${exports.REDIS_KEYS.CHANNELS.SESSION_UPDATE}:${sessionId}`,
    sessionByUser: (userId) => `${exports.REDIS_KEYS.SESSIONS.BY_USER}:${userId}`,
    // User keys
    userDevices: (userId) => `${exports.REDIS_KEYS.USERS.DEVICES}:${userId}`,
    userNotification: (userId) => `${exports.REDIS_KEYS.CHANNELS.USER_NOTIFICATION}:${userId}`,
    // Expiry keys (for Redis keyspace events)
    sessionExpiry: (sessionId) => `${exports.REDIS_KEYS.SESSIONS.EXPIRY}:${sessionId}`,
    sessionEntryCheck: (sessionId) => `${exports.REDIS_KEYS.SESSIONS.ENTRY_CHECK}:${sessionId}`
};
async function setupRedis(redisUrl) {
    exports.redisClient = new ioredis_1.default(redisUrl);
    const redisSubscriber = exports.redisClient.duplicate();
    await redisSubscriber.psubscribe('*');
    console.log('Subscribed to Redis channels');
    return { redisClient: exports.redisClient, redisSubscriber };
}
async function scanAllKeys(pattern) {
    const keys = [];
    let cursor = '0';
    do {
        const [nextCursor, elements] = await exports.redisClient.scan(cursor, 'MATCH', pattern);
        keys.push(...elements);
        cursor = nextCursor;
    } while (cursor !== '0');
    return keys;
}
