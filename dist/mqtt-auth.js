"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MQTTAuthService = exports.CLIENT_PERMISSIONS = exports.TOPIC_PERMISSIONS = void 0;
exports.getMQTTAuthService = getMQTTAuthService;
exports.generateFlutterCredentials = generateFlutterCredentials;
exports.generateAdonisJSCredentials = generateAdonisJSCredentials;
exports.generateRealtimeCredentials = generateRealtimeCredentials;
exports.validateMQTTCredentials = validateMQTTCredentials;
exports.checkTopicPermission = checkTopicPermission;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const crypto_1 = __importDefault(require("crypto"));
// Topic permission patterns
exports.TOPIC_PERMISSIONS = {
    // Read permissions
    READ_LISTINGS: 'listings:read',
    READ_SESSIONS: 'sessions:read',
    READ_USER_DATA: 'user:read',
    READ_SYSTEM: 'system:read',
    // Write permissions
    WRITE_LISTINGS: 'listings:write',
    WRITE_SESSIONS: 'sessions:write',
    WRITE_USER_DATA: 'user:write',
    WRITE_SYSTEM: 'system:write',
    // Admin permissions
    ADMIN_ALL: 'admin:all',
};
// Client type permission mappings
exports.CLIENT_PERMISSIONS = {
    flutter: [
        exports.TOPIC_PERMISSIONS.READ_LISTINGS,
        exports.TOPIC_PERMISSIONS.READ_SESSIONS,
        exports.TOPIC_PERMISSIONS.READ_USER_DATA,
    ],
    adonisjs: [
        exports.TOPIC_PERMISSIONS.WRITE_LISTINGS,
        exports.TOPIC_PERMISSIONS.WRITE_SESSIONS,
        exports.TOPIC_PERMISSIONS.WRITE_USER_DATA,
        exports.TOPIC_PERMISSIONS.READ_SYSTEM,
    ],
    realtime: [
        exports.TOPIC_PERMISSIONS.READ_LISTINGS,
        exports.TOPIC_PERMISSIONS.WRITE_LISTINGS,
        exports.TOPIC_PERMISSIONS.READ_SESSIONS,
        exports.TOPIC_PERMISSIONS.WRITE_SESSIONS,
        exports.TOPIC_PERMISSIONS.READ_USER_DATA,
        exports.TOPIC_PERMISSIONS.WRITE_USER_DATA,
        exports.TOPIC_PERMISSIONS.READ_SYSTEM,
        exports.TOPIC_PERMISSIONS.WRITE_SYSTEM,
    ],
    admin: [exports.TOPIC_PERMISSIONS.ADMIN_ALL],
};
class MQTTAuthService {
    constructor(jwtSecret) {
        this.jwtSecret = jwtSecret || process.env.JWT_SECRET || 'default_mqtt_secret';
    }
    /**
     * Generate JWT token for MQTT client
     */
    generateToken(payload) {
        const now = Math.floor(Date.now() / 1000);
        const fullPayload = {
            ...payload,
            iat: now,
            exp: now + (24 * 60 * 60), // 24 hours
        };
        return jsonwebtoken_1.default.sign(fullPayload, this.jwtSecret);
    }
    /**
     * Verify JWT token
     */
    verifyToken(token) {
        try {
            const decoded = jsonwebtoken_1.default.verify(token, this.jwtSecret);
            return decoded;
        }
        catch (error) {
            console.error('JWT verification failed:', error);
            return null;
        }
    }
    /**
     * Check if client has permission for a specific topic and action
     */
    hasTopicPermission(payload, topic, action) {
        // Admin has access to everything
        if (payload.permissions.includes(exports.TOPIC_PERMISSIONS.ADMIN_ALL)) {
            return true;
        }
        // Check topic-specific permissions
        const topicPattern = this.getTopicPattern(topic);
        const requiredPermission = this.getRequiredPermission(topicPattern, action);
        if (!requiredPermission) {
            return false;
        }
        // Check if user has the required permission
        if (!payload.permissions.includes(requiredPermission)) {
            return false;
        }
        // Additional checks for user-specific topics
        if (topicPattern.includes('{user_id}')) {
            return this.checkUserSpecificAccess(payload, topic);
        }
        return true;
    }
    /**
     * Get topic pattern from actual topic
     */
    getTopicPattern(topic) {
        // Convert actual topics to patterns
        const patterns = [
            { regex: /^gomama\/listings\/status\/[^\/]+$/, pattern: 'gomama/listings/status/{listing_id}' },
            { regex: /^gomama\/listings\/availability\/[^\/]+$/, pattern: 'gomama/listings/availability/{listing_id}' },
            { regex: /^gomama\/sessions\/created\/[^\/]+$/, pattern: 'gomama/sessions/created/{session_id}' },
            { regex: /^gomama\/sessions\/updated\/[^\/]+$/, pattern: 'gomama/sessions/updated/{session_id}' },
            { regex: /^gomama\/sessions\/ended\/[^\/]+$/, pattern: 'gomama/sessions/ended/{session_id}' },
            { regex: /^gomama\/sessions\/cleanup\/[^\/]+$/, pattern: 'gomama/sessions/cleanup/{session_id}' },
            { regex: /^gomama\/users\/notifications\/[^\/]+$/, pattern: 'gomama/users/notifications/{user_id}' },
            { regex: /^gomama\/users\/sessions\/[^\/]+$/, pattern: 'gomama/users/sessions/{user_id}' },
            { regex: /^gomama\/system\/health$/, pattern: 'gomama/system/health' },
            { regex: /^gomama\/system\/metrics$/, pattern: 'gomama/system/metrics' },
        ];
        for (const { regex, pattern } of patterns) {
            if (regex.test(topic)) {
                return pattern;
            }
        }
        return topic; // Return original if no pattern matches
    }
    /**
     * Get required permission for topic pattern and action
     */
    getRequiredPermission(topicPattern, action) {
        const isWrite = action === 'publish';
        if (topicPattern.startsWith('gomama/listings/')) {
            return isWrite ? exports.TOPIC_PERMISSIONS.WRITE_LISTINGS : exports.TOPIC_PERMISSIONS.READ_LISTINGS;
        }
        if (topicPattern.startsWith('gomama/sessions/')) {
            return isWrite ? exports.TOPIC_PERMISSIONS.WRITE_SESSIONS : exports.TOPIC_PERMISSIONS.READ_SESSIONS;
        }
        if (topicPattern.startsWith('gomama/users/')) {
            return isWrite ? exports.TOPIC_PERMISSIONS.WRITE_USER_DATA : exports.TOPIC_PERMISSIONS.READ_USER_DATA;
        }
        if (topicPattern.startsWith('gomama/system/')) {
            return isWrite ? exports.TOPIC_PERMISSIONS.WRITE_SYSTEM : exports.TOPIC_PERMISSIONS.READ_SYSTEM;
        }
        return null;
    }
    /**
     * Check user-specific topic access
     */
    checkUserSpecificAccess(payload, topic) {
        // Extract user ID from topic
        const userIdMatch = topic.match(/\/users\/[^\/]+\/([^\/]+)/);
        if (!userIdMatch) {
            return false;
        }
        const topicUserId = userIdMatch[1];
        // Users can only access their own topics (unless admin)
        return payload.userId === topicUserId || payload.permissions.includes(exports.TOPIC_PERMISSIONS.ADMIN_ALL);
    }
    /**
     * Generate client credentials for different client types
     */
    generateClientCredentials(userId, clientType, deviceId) {
        const permissions = exports.CLIENT_PERMISSIONS[clientType];
        const token = this.generateToken({
            userId,
            clientType,
            permissions,
            deviceId,
        });
        // Use token as password for MQTT authentication
        const username = `${clientType}_${userId}`;
        const password = token;
        return { username, password, token };
    }
    /**
     * Validate MQTT client credentials
     */
    validateCredentials(username, password) {
        try {
            // Password should be the JWT token
            const payload = this.verifyToken(password);
            if (!payload) {
                return { valid: false, error: 'Invalid token' };
            }
            // Verify username matches token
            const expectedUsername = `${payload.clientType}_${payload.userId}`;
            if (username !== expectedUsername) {
                return { valid: false, error: 'Username mismatch' };
            }
            return { valid: true, payload };
        }
        catch (error) {
            return { valid: false, error: 'Authentication failed' };
        }
    }
    /**
     * Generate API key for management operations
     */
    generateApiKey() {
        const apiKey = `gomama_${crypto_1.default.randomBytes(16).toString('hex')}`;
        const secret = crypto_1.default.randomBytes(32).toString('hex');
        return { apiKey, secret };
    }
}
exports.MQTTAuthService = MQTTAuthService;
// Export singleton instance
let authService = null;
function getMQTTAuthService() {
    if (!authService) {
        authService = new MQTTAuthService();
    }
    return authService;
}
// Helper functions for common operations
function generateFlutterCredentials(userId, deviceId) {
    return getMQTTAuthService().generateClientCredentials(userId, 'flutter', deviceId);
}
function generateAdonisJSCredentials(userId = 'adonisjs_server') {
    return getMQTTAuthService().generateClientCredentials(userId, 'adonisjs');
}
function generateRealtimeCredentials(userId = 'realtime_server') {
    return getMQTTAuthService().generateClientCredentials(userId, 'realtime');
}
function validateMQTTCredentials(username, password) {
    return getMQTTAuthService().validateCredentials(username, password);
}
function checkTopicPermission(token, topic, action) {
    const authService = getMQTTAuthService();
    const payload = authService.verifyToken(token);
    if (!payload) {
        return false;
    }
    return authService.hasTopicPermission(payload, topic, action);
}
