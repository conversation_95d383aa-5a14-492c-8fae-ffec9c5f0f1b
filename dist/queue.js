"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sessionQueue = exports.QueueManager = exports.queues = void 0;
const bullmq_1 = require("bullmq");
const redis_1 = require("./redis");
// Queue configurations with proper retry and delay settings
const queueConfig = {
    connection: redis_1.redisClient,
    defaultJobOptions: {
        removeOnComplete: 100, // Keep last 100 completed jobs
        removeOnFail: 50, // Keep last 50 failed jobs
        attempts: 3, // Retry failed jobs 3 times
        backoff: {
            type: 'exponential',
            delay: 2000 // Start with 2 second delay
        }
    }
};
// Define all queues with consistent naming
exports.queues = {
    sessionCleanup: new bullmq_1.Queue('gomama:session-cleanup', queueConfig),
    notifications: new bullmq_1.Queue('gomama:notifications', queueConfig),
    systemTasks: new bullmq_1.Queue('gomama:system-tasks', queueConfig)
};
// Helper functions for adding jobs
exports.QueueManager = {
    // Session cleanup jobs
    addSessionCleanup: (data) => exports.queues.sessionCleanup.add('cleanup', data),
    // Notification jobs
    addNotification: (data) => exports.queues.notifications.add(data.type, data),
    // System task jobs
    addSystemTask: (data) => exports.queues.systemTasks.add(data.task, data),
    // Get queue stats for monitoring
    getQueueStats: async () => {
        const stats = {};
        for (const [name, queue] of Object.entries(exports.queues)) {
            stats[name] = {
                waiting: await queue.getWaiting(),
                active: await queue.getActive(),
                completed: await queue.getCompleted(),
                failed: await queue.getFailed()
            };
        }
        return stats;
    }
};
// Export individual queues for backward compatibility
exports.sessionQueue = exports.queues.sessionCleanup;
