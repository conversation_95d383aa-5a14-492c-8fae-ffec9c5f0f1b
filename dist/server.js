"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const dotenv_1 = __importDefault(require("dotenv"));
const redis_1 = require("./redis");
const firebase_1 = require("./firebase");
const events_1 = require("./events");
const monitoring_1 = require("./monitoring");
const workers_1 = require("./workers");
const queue_1 = require("./queue");
const mqtt_1 = require("./mqtt");
const redis_mqtt_bridge_1 = require("./redis-mqtt-bridge");
const mqtt_auth_1 = require("./mqtt-auth");
dotenv_1.default.config();
const port = Number(process.env.PORT || 9001);
const redisUrl = `redis://${process.env.REDIS_HOST}:${process.env.REDIS_PORT}`;
async function startServer() {
    // Initialize monitoring
    (0, monitoring_1.setupMonitoring)();
    const { redisClient, redisSubscriber } = await (0, redis_1.setupRedis)(redisUrl);
    // Setup MQTT client
    const mqttClient = (0, mqtt_1.setupMQTT)();
    // Wait for MQTT connection before proceeding
    await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
            reject(new Error('MQTT connection timeout'));
        }, 30000); // 30 second timeout
        mqttClient.once('connected', () => {
            clearTimeout(timeout);
            resolve();
        });
        mqttClient.once('error', (error) => {
            clearTimeout(timeout);
            reject(error);
        });
    });
    // Setup Redis-MQTT Bridge
    const bridge = (0, redis_mqtt_bridge_1.setupRedisMQTTBridge)(redisClient, mqttClient);
    (0, firebase_1.setupFirebase)(redisClient);
    (0, events_1.setupRedisKeyEvents)(redisSubscriber);
    // Start BullMQ workers
    console.log('Starting BullMQ workers...');
    Object.values(workers_1.workers).forEach((worker) => {
        console.log(`Started worker: ${worker.name}`);
    });
    const app = (0, express_1.default)();
    // Middleware for parsing JSON
    app.use(express_1.default.json());
    // Add a metrics endpoint
    app.get('/metrics', async (req, res) => {
        const metricsApiKey = process.env.METRICS_API_KEY;
        const authHeader = req.headers.authorization;
        if (!metricsApiKey || authHeader !== `Bearer ${metricsApiKey}`) {
            res.status(401).json({ error: 'Unauthorized' });
            return;
        }
        const systemMetrics = (0, monitoring_1.getSystemMetrics)();
        const queueStats = await queue_1.QueueManager.getQueueStats();
        const mqttClient = (0, mqtt_1.getMQTTClient)();
        const bridge = (0, redis_mqtt_bridge_1.getRedisMQTTBridge)();
        const metrics = {
            ...systemMetrics,
            queues: queueStats,
            mqtt: {
                connected: (mqttClient === null || mqttClient === void 0 ? void 0 : mqttClient.isClientConnected()) || false,
                clientId: process.env.MQTT_CLIENT_ID
            },
            bridge: (bridge === null || bridge === void 0 ? void 0 : bridge.getStats()) || { enabled: false },
            timestamp: Date.now()
        };
        res.json(metrics);
    });
    // MQTT Authentication endpoints
    app.post('/mqtt/auth', (req, res) => {
        try {
            const { clientid, username, password, peerhost } = req.body;
            const result = (0, mqtt_auth_1.validateMQTTCredentials)(username, password);
            if (result.valid) {
                res.status(200).send('allow');
            }
            else {
                res.status(401).send('deny');
            }
        }
        catch (error) {
            res.status(400).send('deny');
        }
    });
    app.post('/mqtt/superuser', (req, res) => {
        try {
            const { clientid, username, peerhost } = req.body;
            // Only realtime and admin clients are superusers
            const isSuperuser = username.startsWith('admin_') || username.startsWith('realtime_');
            res.status(200).send(isSuperuser ? 'allow' : 'deny');
        }
        catch (error) {
            res.status(400).send('deny');
        }
    });
    app.post('/mqtt/acl', (req, res) => {
        try {
            const { access, username, clientid, topic } = req.body;
            // Extract token from username (format: clienttype_userid)
            const parts = username.split('_');
            if (parts.length < 2) {
                res.status(401).send('deny');
                return;
            }
            // For now, allow all authenticated users (token validation was done in auth step)
            // In production, implement proper topic-based ACL using checkTopicPermission
            const action = access === '1' ? 'subscribe' : 'publish';
            res.status(200).send('allow');
        }
        catch (error) {
            res.status(400).send('deny');
        }
    });
    // Default route
    app.get('*', (req, res) => {
        res.send('Nothing to see here!');
    });
    // Start the server
    const server = app.listen(port, () => {
        console.log('Listening to port ' + port);
    });
    // Setup Redis subscriber with MQTT bridge
    redisSubscriber.on('pmessage', async (_, channel, message) => {
        console.log("Received from Redis channel '%s'", channel);
        // Bridge to MQTT for new clients
        const bridge = (0, redis_mqtt_bridge_1.getRedisMQTTBridge)();
        if (bridge) {
            await bridge.bridgeRedisToMQTT(channel, message);
        }
    });
    // Setup session checking
}
startServer().catch(console.error);
// ------------------------
// gomama_rt broadcast
// ------------------------
// .post("/broadcast", (res, req) => {
//   readJson(
//     res,
//     (body: { topic: string; data: string }) => {
//       const topic = body.topic
//       const data = body.data
//       if (topic && data) {
//         app.publish(topic, data)
//       }
//       res.writeStatus("200 OK").end("Message published")
//     },
//     () => {
//       /* Request was prematurely aborted or invalid or missing, stop reading */
//       console.log("Invalid JSON or no data at all!")
//       res.writeStatus("400 Bad Request").end("Invalid JSON")
//     }
//   )
// })
