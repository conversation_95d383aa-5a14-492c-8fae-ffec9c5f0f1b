"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GoMamaMQTTClient = exports.MQTTTopicBuilder = exports.MQTT_TOPICS = void 0;
exports.setupMQTT = setupMQTT;
exports.getMQTTClient = getMQTTClient;
const mqtt_1 = __importDefault(require("mqtt"));
const events_1 = require("events");
const queue_1 = require("./queue");
// MQTT Topic structure for GoMama
exports.MQTT_TOPICS = {
    // Listings
    LISTING_STATUS: 'gomama/listings/status', // Pattern: gomama/listings/status/{listing_id}
    LISTING_AVAILABILITY: 'gomama/listings/availability', // Pattern: gomama/listings/availability/{listing_id}
    // Sessions  
    SESSION_CREATED: 'gomama/sessions/created', // Pattern: gomama/sessions/created/{session_id}
    SESSION_UPDATED: 'gomama/sessions/updated', // Pattern: gomama/sessions/updated/{session_id}
    SESSION_ENDED: 'gomama/sessions/ended', // Pattern: gomama/sessions/ended/{session_id}
    SESSION_CLEANUP: 'gomama/sessions/cleanup', // Pattern: gomama/sessions/cleanup/{session_id}
    // Users & Notifications
    USER_NOTIFICATION: 'gomama/users/notifications', // Pattern: gomama/users/notifications/{user_id}
    USER_SESSION_UPDATE: 'gomama/users/sessions', // Pattern: gomama/users/sessions/{user_id}
    // System
    SYSTEM_HEALTH: 'gomama/system/health',
    SYSTEM_METRICS: 'gomama/system/metrics',
    // Internal (realtime service only)
    INTERNAL_FIREBASE_UPDATE: 'gomama/internal/firebase', // Firebase → Realtime
    INTERNAL_REDIS_EVENT: 'gomama/internal/redis', // Redis events → Realtime
};
// MQTT Topic builders
exports.MQTTTopicBuilder = {
    // Listing topics
    listingStatus: (listingId) => `${exports.MQTT_TOPICS.LISTING_STATUS}/${listingId}`,
    listingAvailability: (listingId) => `${exports.MQTT_TOPICS.LISTING_AVAILABILITY}/${listingId}`,
    // Session topics
    sessionCreated: (sessionId) => `${exports.MQTT_TOPICS.SESSION_CREATED}/${sessionId}`,
    sessionUpdated: (sessionId) => `${exports.MQTT_TOPICS.SESSION_UPDATED}/${sessionId}`,
    sessionEnded: (sessionId) => `${exports.MQTT_TOPICS.SESSION_ENDED}/${sessionId}`,
    sessionCleanup: (sessionId) => `${exports.MQTT_TOPICS.SESSION_CLEANUP}/${sessionId}`,
    // User topics
    userNotification: (userId) => `${exports.MQTT_TOPICS.USER_NOTIFICATION}/${userId}`,
    userSessionUpdate: (userId) => `${exports.MQTT_TOPICS.USER_SESSION_UPDATE}/${userId}`,
    // Internal topics
    firebaseUpdate: (docId) => `${exports.MQTT_TOPICS.INTERNAL_FIREBASE_UPDATE}/${docId}`,
    redisEvent: (eventType) => `${exports.MQTT_TOPICS.INTERNAL_REDIS_EVENT}/${eventType}`,
};
// MQTT Client wrapper class
class GoMamaMQTTClient extends events_1.EventEmitter {
    constructor(options) {
        super();
        this.options = options;
        this.client = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 10;
        this.reconnectInterval = 5000; // 5 seconds
        this.setupClient();
    }
    setupClient() {
        const clientOptions = {
            ...this.options,
            clientId: this.options.clientId || `gomama_realtime_${Date.now()}`,
            clean: false, // Persistent session
            keepalive: 60,
            reconnectPeriod: this.reconnectInterval,
            connectTimeout: 30000,
            will: {
                topic: exports.MQTT_TOPICS.SYSTEM_HEALTH,
                payload: JSON.stringify({
                    timestamp: Date.now(),
                    source: 'realtime',
                    type: 'disconnect',
                    data: { clientId: this.options.clientId, reason: 'unexpected' }
                }),
                qos: 1,
                retain: false
            }
        };
        this.client = mqtt_1.default.connect(clientOptions);
        this.setupEventHandlers();
    }
    setupEventHandlers() {
        if (!this.client)
            return;
        this.client.on('connect', () => {
            console.log('✅ MQTT Client connected to broker');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.emit('connected');
            // Subscribe to topics that this realtime service should handle
            this.subscribeToRealtimeTopics();
            // Publish connection status
            this.publishSystemHealth('connected');
        });
        this.client.on('disconnect', () => {
            console.log('❌ MQTT Client disconnected from broker');
            this.isConnected = false;
            this.emit('disconnected');
        });
        this.client.on('reconnect', () => {
            var _a;
            this.reconnectAttempts++;
            console.log(`🔄 MQTT Client reconnecting... (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            if (this.reconnectAttempts >= this.maxReconnectAttempts) {
                console.error('❌ Max reconnection attempts reached. Stopping reconnection.');
                (_a = this.client) === null || _a === void 0 ? void 0 : _a.end(true);
            }
        });
        this.client.on('error', (error) => {
            console.error('❌ MQTT Client error:', error);
            this.emit('error', error);
        });
        this.client.on('message', (topic, payload, packet) => {
            try {
                const message = JSON.parse(payload.toString());
                this.handleIncomingMessage(topic, message, packet);
            }
            catch (error) {
                console.error('❌ Error parsing MQTT message:', error);
                console.error('Topic:', topic, 'Payload:', payload.toString());
            }
        });
    }
    async subscribeToRealtimeTopics() {
        if (!this.client || !this.isConnected)
            return;
        // Subscribe to topics that the realtime service should handle
        const subscriptions = [
            // From AdonisJS - session events
            { topic: `${exports.MQTT_TOPICS.SESSION_CREATED}/+`, qos: 1 },
            { topic: `${exports.MQTT_TOPICS.SESSION_UPDATED}/+`, qos: 1 },
            { topic: `${exports.MQTT_TOPICS.SESSION_ENDED}/+`, qos: 1 },
            // From AdonisJS - listing events  
            { topic: `${exports.MQTT_TOPICS.LISTING_STATUS}/+`, qos: 1 },
            { topic: `${exports.MQTT_TOPICS.LISTING_AVAILABILITY}/+`, qos: 1 },
            // Internal events
            { topic: `${exports.MQTT_TOPICS.INTERNAL_FIREBASE_UPDATE}/+`, qos: 1 },
            { topic: `${exports.MQTT_TOPICS.INTERNAL_REDIS_EVENT}/+`, qos: 1 },
            // System events
            { topic: exports.MQTT_TOPICS.SYSTEM_HEALTH, qos: 0 },
        ];
        for (const sub of subscriptions) {
            this.client.subscribe(sub.topic, { qos: sub.qos }, (err) => {
                if (err) {
                    console.error(`❌ Failed to subscribe to ${sub.topic}:`, err);
                }
                else {
                    console.log(`✅ Subscribed to ${sub.topic}`);
                }
            });
        }
    }
    handleIncomingMessage(topic, message, packet) {
        console.log(`📨 Received MQTT message on ${topic}:`, message.type);
        // Route messages based on topic patterns
        if (topic.startsWith(exports.MQTT_TOPICS.SESSION_CREATED)) {
            this.handleSessionCreated(topic, message);
        }
        else if (topic.startsWith(exports.MQTT_TOPICS.SESSION_UPDATED)) {
            this.handleSessionUpdated(topic, message);
        }
        else if (topic.startsWith(exports.MQTT_TOPICS.SESSION_ENDED)) {
            this.handleSessionEnded(topic, message);
        }
        else if (topic.startsWith(exports.MQTT_TOPICS.LISTING_STATUS)) {
            this.handleListingStatusUpdate(topic, message);
        }
        else if (topic.startsWith(exports.MQTT_TOPICS.INTERNAL_FIREBASE_UPDATE)) {
            this.handleFirebaseUpdate(topic, message);
        }
        else if (topic.startsWith(exports.MQTT_TOPICS.INTERNAL_REDIS_EVENT)) {
            this.handleRedisEvent(topic, message);
        }
        else {
            console.log(`ℹ️ Unhandled MQTT topic: ${topic}`);
        }
        // Emit event for other parts of the application
        this.emit('message', { topic, message, packet });
    }
    // Message handlers
    handleSessionCreated(topic, message) {
        const sessionId = topic.split('/').pop();
        console.log(`🆕 Session created: ${sessionId}`);
        // Forward to Flutter clients via user-specific topic
        if (message.data.userId) {
            this.publishUserSessionUpdate(message.data.userId, {
                type: 'session_created',
                sessionId,
                data: message.data
            });
        }
    }
    handleSessionUpdated(topic, message) {
        const sessionId = topic.split('/').pop();
        console.log(`🔄 Session updated: ${sessionId}`);
        // Forward to Flutter clients
        if (message.data.userId) {
            this.publishUserSessionUpdate(message.data.userId, {
                type: 'session_updated',
                sessionId,
                data: message.data
            });
        }
    }
    handleSessionEnded(topic, message) {
        const sessionId = topic.split('/').pop();
        console.log(`🔚 Session ended: ${sessionId}`);
        // Trigger cleanup job
        queue_1.QueueManager.addSessionCleanup({
            sessionId: sessionId,
            userId: message.data.userId,
            reason: 'ended'
        });
        // Forward to Flutter clients
        if (message.data.userId) {
            this.publishUserSessionUpdate(message.data.userId, {
                type: 'session_ended',
                sessionId,
                data: message.data
            });
        }
    }
    handleListingStatusUpdate(topic, message) {
        const listingId = topic.split('/').pop();
        console.log(`🏠 Listing status updated: ${listingId} -> ${message.data.status}`);
        // This will be forwarded to Flutter clients who are subscribed to this listing
        // The MQTT broker handles the fan-out to subscribed clients
    }
    handleFirebaseUpdate(topic, message) {
        console.log(`🔥 Firebase update received:`, message.type);
        // Handle Firebase-specific updates
    }
    handleRedisEvent(topic, message) {
        console.log(`📊 Redis event received:`, message.type);
        // Handle Redis keyspace events
    }
    // Publishing methods
    async publish(topic, message, options = {}) {
        if (!this.client || !this.isConnected) {
            console.error('❌ Cannot publish: MQTT client not connected');
            return false;
        }
        const payload = JSON.stringify(message);
        const publishOptions = {
            qos: options.qos || 1,
            retain: options.retain || false
        };
        return new Promise((resolve) => {
            this.client.publish(topic, payload, publishOptions, (err) => {
                if (err) {
                    console.error(`❌ Failed to publish to ${topic}:`, err);
                    resolve(false);
                }
                else {
                    console.log(`✅ Published to ${topic}`);
                    resolve(true);
                }
            });
        });
    }
    // Convenience publishing methods
    async publishUserNotification(userId, notification) {
        return this.publish(exports.MQTTTopicBuilder.userNotification(userId), {
            timestamp: Date.now(),
            source: 'realtime',
            type: 'notification',
            data: notification
        }, { qos: 1, retain: false });
    }
    async publishUserSessionUpdate(userId, sessionUpdate) {
        return this.publish(exports.MQTTTopicBuilder.userSessionUpdate(userId), {
            timestamp: Date.now(),
            source: 'realtime',
            type: 'session_update',
            data: sessionUpdate
        }, { qos: 1, retain: true }); // Retain for offline clients
    }
    async publishListingStatus(listingId, status) {
        return this.publish(exports.MQTTTopicBuilder.listingStatus(listingId), {
            timestamp: Date.now(),
            source: 'realtime',
            type: 'status_update',
            data: { listingId, status }
        }, { qos: 1, retain: true }); // Retain for offline clients
    }
    async publishSystemHealth(status, data) {
        return this.publish(exports.MQTT_TOPICS.SYSTEM_HEALTH, {
            timestamp: Date.now(),
            source: 'realtime',
            type: 'health_status',
            data: { status, clientId: this.options.clientId, ...data }
        }, { qos: 0, retain: false });
    }
    // Connection management
    isClientConnected() {
        return this.isConnected;
    }
    async disconnect() {
        if (this.client) {
            await this.publishSystemHealth('disconnected', { reason: 'graceful_shutdown' });
            this.client.end(true);
            this.isConnected = false;
        }
    }
}
exports.GoMamaMQTTClient = GoMamaMQTTClient;
// Export singleton instance
let mqttClient = null;
function setupMQTT() {
    if (mqttClient) {
        return mqttClient;
    }
    const brokerUrl = `mqtt://${process.env.MQTT_BROKER_HOST || 'localhost'}:${process.env.MQTT_BROKER_PORT || 1883}`;
    const options = {
        clientId: process.env.MQTT_CLIENT_ID || `gomama_realtime_${Date.now()}`,
        username: process.env.MQTT_USERNAME,
        password: process.env.MQTT_PASSWORD,
        servers: [{ host: process.env.MQTT_BROKER_HOST || 'localhost', port: Number(process.env.MQTT_BROKER_PORT || 1883) }]
    };
    mqttClient = new GoMamaMQTTClient(options);
    console.log(`🚀 Setting up MQTT client connecting to: ${brokerUrl}`);
    return mqttClient;
}
function getMQTTClient() {
    return mqttClient;
}
