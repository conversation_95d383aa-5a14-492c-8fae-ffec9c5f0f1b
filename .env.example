# GoMama Realtime Environment Configuration

# Server Configuration
PORT=9001
NODE_ENV=development

# Redis Configuration (for data storage)
REDIS_HOST=localhost
REDIS_PORT=6379

# MQTT Broker Configuration
MQTT_BROKER_HOST=localhost
MQTT_BROKER_PORT=1883
MQTT_CLIENT_ID=gomama_realtime_server
MQTT_USERNAME=realtime_client
MQTT_PASSWORD=realtime_pass

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here

# Firebase Configuration
FIREBASE_SERVICE_ACCOUNT_PATH=./serviceAccountKey.json
FIREBASE_URL=https://your-project.firebaseio.com

# Monitoring & Metrics
METRICS_API_KEY=your_metrics_api_key_here

# EMQX Management API (optional)
EMQX_API_KEY=gomama_realtime_api
EMQX_API_SECRET=gomama_realtime_secret_2024
EMQX_DASHBOARD_URL=http://localhost:18083
