import Redis from 'ioredis'
import { ListingSession, TopicWebSocket } from './types'

// Improved Redis key structure with consistent naming and clear organization
export const REDIS_KEYS = {
  // Listings - Hash structure for efficient lookups
  LISTINGS: {
    ACTIVE: 'gomama:listings:active', // Hash: {listing_id: status}
    STATUS_HISTORY: 'gomama:listings:history' // Hash: {listing_id: json_history}
  },

  // Sessions - Organized by data type
  SESSIONS: {
    ACTIVE: 'gomama:sessions:active', // Hash: {session_id: json_data}
    BY_USER: 'gomama:sessions:by_user', // Hash: {user_id: session_id}
    EXPIRY: 'gomama:sessions:expiry', // Sorted Set: {session_id: expiry_timestamp}
    ENTRY_CHECK: 'gomama:sessions:entry_check' // Sorted Set: {session_id: deadline_timestamp}
  },

  // Users & Devices
  USERS: {
    DEVICES: 'gomama:users:devices', // Hash: {user_id: json_device_array}
    SESSIONS: 'gomama:users:sessions' // Hash: {user_id: current_session_id}
  },

  // Pub/Sub channels for real-time updates
  CHANNELS: {
    LISTING_STATUS: 'gomama:channels:listing_status', // Pattern: listing_status:{listing_id}
    SESSION_UPDATE: 'gomama:channels:session_update', // Pattern: session_update:{session_id}
    USER_NOTIFICATION: 'gomama:channels:user_notification' // Pattern: user_notification:{user_id}
  },

  // System keys
  SYSTEM: {
    METRICS: 'gomama:system:metrics',
    HEALTH: 'gomama:system:health'
  }
} as const

// Helper functions for key construction
export const RedisKeyBuilder = {
  // Listing keys
  listingStatus: (listingId: string) => `${REDIS_KEYS.CHANNELS.LISTING_STATUS}:${listingId}`,

  // Session keys
  sessionUpdate: (sessionId: string) => `${REDIS_KEYS.CHANNELS.SESSION_UPDATE}:${sessionId}`,
  sessionByUser: (userId: string) => `${REDIS_KEYS.SESSIONS.BY_USER}:${userId}`,

  // User keys
  userDevices: (userId: string) => `${REDIS_KEYS.USERS.DEVICES}:${userId}`,
  userNotification: (userId: string) => `${REDIS_KEYS.CHANNELS.USER_NOTIFICATION}:${userId}`,

  // Expiry keys (for Redis keyspace events)
  sessionExpiry: (sessionId: string) => `${REDIS_KEYS.SESSIONS.EXPIRY}:${sessionId}`,
  sessionEntryCheck: (sessionId: string) => `${REDIS_KEYS.SESSIONS.ENTRY_CHECK}:${sessionId}`
} as const

export let redisClient: Redis

export async function setupRedis(redisUrl: string) {
  redisClient = new Redis(redisUrl)
  const redisSubscriber = redisClient.duplicate()

  await redisSubscriber.psubscribe('*')
  console.log('Subscribed to Redis channels')

  return { redisClient, redisSubscriber }
}

export async function handleSubscribe(ws: TopicWebSocket, topic: string, redisClient: Redis) {
  // Add topic to user's subscribed topics
  ws.getUserData().subscribedTopics.add(topic)
  ws.subscribe(topic)
  console.log(`Client subscribed to topic: ${topic}`)

  try {
    // Parse topic format (topic:id)
    const [key, id] = topic.split(':')

    if (!key || !id) {
      console.error(`Invalid topic format: ${topic}`)
      return
    }

    // Handle different types of subscriptions more efficiently
    if (key === 'active-listings') {
      // For active listings, just get the specific listing status
      const initialData = await redisClient.hget(REDIS_KEYS.LISTINGS.ACTIVE, id)

      if (initialData) {
        ws.send(JSON.stringify({ topic, data: initialData }))
      }
    } else {
      // For other topics (like session data)
      const redisData = await redisClient.hgetall(topic)

      if (isEmpty(redisData)) {
        return // No data to send
      }

      // Process data based on whether it's a ListingSession or not
      if (redisData['id'] == null) {
        // Simple JSON data
        ws.send(JSON.stringify({ topic, data: JSON.stringify(redisData) }))
      } else {
        // ListingSession data
        try {
          const listingSession = ListingSession.fromJSON(redisData)
          ws.send(JSON.stringify({ topic, data: JSON.stringify(listingSession) }))
        } catch (error) {
          console.error(`Error processing ListingSession data for topic ${topic}:`, error)
        }
      }
    }
  } catch (error) {
    console.error(`Error in handleSubscribe for topic ${topic}:`, error)
  }
}

export function handleUnsubscribe(ws: TopicWebSocket, topic: string) {
  ws.getUserData().subscribedTopics.delete(topic)
  ws.unsubscribe(topic)
  console.log(`Client unsubscribed from topic: ${topic}`)
}

export async function scanAllKeys(pattern: string): Promise<string[]> {
  const keys: string[] = []
  let cursor = '0'

  do {
    const [nextCursor, elements] = await redisClient.scan(cursor, 'MATCH', pattern)
    keys.push(...elements)
    cursor = nextCursor
  } while (cursor !== '0')

  return keys
}

const isEmpty = (value: unknown): value is null | undefined | '' | Record<never, never> => {
  if (value === null || value === undefined || value === '') return true
  if (typeof value === 'object' && !Array.isArray(value) && Object.keys(value).length === 0)
    return true
  return false
}
