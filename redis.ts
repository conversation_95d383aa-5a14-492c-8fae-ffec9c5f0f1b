import Redis from 'ioredis'

// Improved Redis key structure with consistent naming and clear organization
export const REDIS_KEYS = {
  // Listings - Hash structure for efficient lookups
  LISTINGS: {
    ACTIVE: 'gomama:listings:active', // Hash: {listing_id: status}
    STATUS_HISTORY: 'gomama:listings:history' // Hash: {listing_id: json_history}
  },

  // Sessions - Organized by data type
  SESSIONS: {
    ACTIVE: 'gomama:sessions:active', // Hash: {session_id: json_data}
    BY_USER: 'gomama:sessions:by_user', // Hash: {user_id: session_id}
    EXPIRY: 'gomama:sessions:expiry', // Sorted Set: {session_id: expiry_timestamp}
    ENTRY_CHECK: 'gomama:sessions:entry_check' // Sorted Set: {session_id: deadline_timestamp}
  },

  // Users & Devices
  USERS: {
    DEVICES: 'gomama:users:devices', // Hash: {user_id: json_device_array}
    SESSIONS: 'gomama:users:sessions' // Hash: {user_id: current_session_id}
  },

  // Pub/Sub channels for real-time updates
  CHANNELS: {
    LISTING_STATUS: 'gomama:channels:listing_status', // Pattern: listing_status:{listing_id}
    SESSION_UPDATE: 'gomama:channels:session_update', // Pattern: session_update:{session_id}
    USER_NOTIFICATION: 'gomama:channels:user_notification' // Pattern: user_notification:{user_id}
  },

  // System keys
  SYSTEM: {
    METRICS: 'gomama:system:metrics',
    HEALTH: 'gomama:system:health'
  }
} as const

// Helper functions for key construction
export const RedisKeyBuilder = {
  // Listing keys
  listingStatus: (listingId: string) => `${REDIS_KEYS.CHANNELS.LISTING_STATUS}:${listingId}`,

  // Session keys
  sessionUpdate: (sessionId: string) => `${REDIS_KEYS.CHANNELS.SESSION_UPDATE}:${sessionId}`,
  sessionByUser: (userId: string) => `${REDIS_KEYS.SESSIONS.BY_USER}:${userId}`,

  // User keys
  userDevices: (userId: string) => `${REDIS_KEYS.USERS.DEVICES}:${userId}`,
  userNotification: (userId: string) => `${REDIS_KEYS.CHANNELS.USER_NOTIFICATION}:${userId}`,

  // Expiry keys (for Redis keyspace events)
  sessionExpiry: (sessionId: string) => `${REDIS_KEYS.SESSIONS.EXPIRY}:${sessionId}`,
  sessionEntryCheck: (sessionId: string) => `${REDIS_KEYS.SESSIONS.ENTRY_CHECK}:${sessionId}`
} as const

export let redisClient: Redis

export async function setupRedis(redisUrl: string) {
  redisClient = new Redis(redisUrl)
  const redisSubscriber = redisClient.duplicate()

  await redisSubscriber.psubscribe('*')
  console.log('Subscribed to Redis channels')

  return { redisClient, redisSubscriber }
}



export async function scanAllKeys(pattern: string): Promise<string[]> {
  const keys: string[] = []
  let cursor = '0'

  do {
    const [nextCursor, elements] = await redisClient.scan(cursor, 'MATCH', pattern)
    keys.push(...elements)
    cursor = nextCursor
  } while (cursor !== '0')

  return keys
}


