import { Worker, Job } from 'bullmq'
import { getMessaging } from 'firebase-admin/messaging'
import { redisClient, REDIS_KEYS, RedisKeyBuilder } from './redis'
import { SessionCleanupJob, NotificationJob, SystemTaskJob } from './queue'

// Session cleanup worker
export const sessionCleanupWorker = new Worker(
  'gomama:session-cleanup',
  async (job: Job<SessionCleanupJob>) => {
    const { sessionId, userId, reason } = job.data
    console.log(`Processing session cleanup for ${sessionId}, reason: ${reason}`)

    try {
      // Get session data before cleanup
      const sessionData = await redisClient.hget(REDIS_KEYS.SESSIONS.ACTIVE, sessionId)

      if (!sessionData) {
        console.log(`Session ${sessionId} not found, skipping cleanup`)
        return
      }

      const session = JSON.parse(sessionData)
      const actualUserId = userId || session.user_id

      // Pipeline cleanup operations
      const pipeline = redisClient.pipeline()

      // Remove session from active sessions
      pipeline.hdel(REDIS_KEYS.SESSIONS.ACTIVE, sessionId)

      // Remove user session mapping
      if (actualUserId) {
        pipeline.hdel(REDIS_KEYS.SESSIONS.BY_USER, actualUserId)
      }

      // Remove from expiry tracking
      pipeline.zrem(REDIS_KEYS.SESSIONS.EXPIRY, sessionId)
      pipeline.zrem(REDIS_KEYS.SESSIONS.ENTRY_CHECK, sessionId)

      // Execute cleanup
      await pipeline.exec()

      console.log(`Successfully cleaned up session ${sessionId}`)

      // Publish cleanup notification to AdonisJS
      await redisClient.publish(
        'gomama:session:cleanup',
        JSON.stringify({
          sessionId,
          userId: actualUserId,
          reason,
          timestamp: Date.now()
        })
      )
    } catch (error) {
      console.error(`Error cleaning up session ${sessionId}:`, error)
      throw error // This will mark the job as failed and trigger retry
    }
  },
  { connection: redisClient }
)

// Notification worker
export const notificationWorker = new Worker(
  'gomama:notifications',
  async (job: Job<NotificationJob>) => {
    const { type, userId, message, devices } = job.data
    console.log(`Processing ${type} notification for user ${userId}`)

    try {
      if (!devices || devices.length === 0) {
        console.log(`No devices found for user ${userId}, skipping notification`)
        return
      }

      const messaging = getMessaging()
      const multicastMessage = {
        notification: {
          title: message.title,
          body: message.body
        },
        data: message.data || {},
        tokens: devices
      }

      const response = await messaging.sendEachForMulticast(multicastMessage)

      console.log(
        `Notification sent: ${response.successCount} successful, ${response.failureCount} failed`
      )

      // Log failed tokens for debugging
      if (response.failureCount > 0) {
        response.responses.forEach((resp, idx) => {
          if (!resp.success) {
            console.error(`Failed to send to token ${devices[idx]}:`, resp.error)
          }
        })
      }
    } catch (error) {
      console.error(`Error sending ${type} notification to user ${userId}:`, error)
      throw error
    }
  },
  { connection: redisClient }
)

// System tasks worker
export const systemTasksWorker = new Worker(
  'gomama:system-tasks',
  async (job: Job<SystemTaskJob>) => {
    const { task, params } = job.data
    console.log(`Processing system task: ${task}`)

    try {
      switch (task) {
        case 'cleanup_expired_keys':
          await cleanupExpiredKeys()
          break

        case 'update_metrics':
          await updateSystemMetrics()
          break

        case 'health_check':
          await performHealthCheck()
          break

        default:
          throw new Error(`Unknown system task: ${task}`)
      }

      console.log(`System task ${task} completed successfully`)
    } catch (error) {
      console.error(`Error processing system task ${task}:`, error)
      throw error
    }
  },
  { connection: redisClient }
)

// System task implementations
async function cleanupExpiredKeys() {
  // Clean up any orphaned keys or expired data
  const expiredSessions = await redisClient.zrangebyscore(REDIS_KEYS.SESSIONS.EXPIRY, 0, Date.now())

  if (expiredSessions.length > 0) {
    console.log(`Found ${expiredSessions.length} expired sessions to cleanup`)
    // These should be handled by Redis keyspace events, but this is a backup
  }
}

async function updateSystemMetrics() {
  // Update system metrics in Redis
  const metrics = {
    timestamp: Date.now(),
    memory: process.memoryUsage(),
    uptime: process.uptime()
  }

  await redisClient.hset(REDIS_KEYS.SYSTEM.METRICS, 'current', JSON.stringify(metrics))
}

async function performHealthCheck() {
  // Perform basic health checks
  const checks = {
    redis: await checkRedisHealth(),
    queues: await checkQueueHealth(),
    timestamp: Date.now()
  }

  await redisClient.hset(REDIS_KEYS.SYSTEM.HEALTH, 'status', JSON.stringify(checks))
}

async function checkRedisHealth(): Promise<boolean> {
  try {
    await redisClient.ping()
    return true
  } catch {
    return false
  }
}

async function checkQueueHealth(): Promise<Record<string, any>> {
  // This would check queue health - simplified for now
  return { status: 'healthy' }
}

// Error handlers for workers
sessionCleanupWorker.on('failed', (job, err) => {
  console.error(`Session cleanup job ${job?.id} failed:`, err)
})

notificationWorker.on('failed', (job, err) => {
  console.error(`Notification job ${job?.id} failed:`, err)
})

systemTasksWorker.on('failed', (job, err) => {
  console.error(`System task job ${job?.id} failed:`, err)
})

// Success handlers
sessionCleanupWorker.on('completed', (job) => {
  console.log(`Session cleanup job ${job.id} completed`)
})

notificationWorker.on('completed', (job) => {
  console.log(`Notification job ${job.id} completed`)
})

systemTasksWorker.on('completed', (job) => {
  console.log(`System task job ${job.id} completed`)
})

export const workers = {
  sessionCleanup: sessionCleanupWorker,
  notifications: notificationWorker,
  systemTasks: systemTasksWorker
} as const
