import { Queue, Worker, Job } from 'bullmq'
import { redisClient } from './redis'

// Queue configurations with proper retry and delay settings
const queueConfig = {
  connection: redisClient,
  defaultJobOptions: {
    removeOnComplete: 100, // Keep last 100 completed jobs
    removeOnFail: 50, // Keep last 50 failed jobs
    attempts: 3, // Retry failed jobs 3 times
    backoff: {
      type: 'exponential',
      delay: 2000 // Start with 2 second delay
    }
  }
}

// Define all queues with consistent naming
export const queues = {
  sessionCleanup: new Queue('gomama:session-cleanup', queueConfig),
  notifications: new Queue('gomama:notifications', queueConfig),
  systemTasks: new Queue('gomama:system-tasks', queueConfig)
} as const

// Job type definitions for better type safety
export interface SessionCleanupJob {
  sessionId: string
  userId?: string
  reason?: 'expired' | 'terminated' | 'idle'
}

export interface NotificationJob {
  type: 'feedback' | 'reminder' | 'alert'
  userId: string
  sessionId?: string
  message: {
    title: string
    body: string
    data?: Record<string, string>
  }
  devices: string[]
}

export interface SystemTaskJob {
  task: 'cleanup_expired_keys' | 'update_metrics' | 'health_check'
  params?: Record<string, any>
}

// Helper functions for adding jobs
export const QueueManager = {
  // Session cleanup jobs
  addSessionCleanup: (data: SessionCleanupJob) => queues.sessionCleanup.add('cleanup', data),

  // Notification jobs
  addNotification: (data: NotificationJob) => queues.notifications.add(data.type, data),

  // System task jobs
  addSystemTask: (data: SystemTaskJob) => queues.systemTasks.add(data.task, data),

  // Get queue stats for monitoring
  getQueueStats: async () => {
    const stats = {}
    for (const [name, queue] of Object.entries(queues)) {
      stats[name] = {
        waiting: await queue.getWaiting(),
        active: await queue.getActive(),
        completed: await queue.getCompleted(),
        failed: await queue.getFailed()
      }
    }
    return stats
  }
} as const

// Export individual queues for backward compatibility
export const sessionQueue = queues.sessionCleanup
