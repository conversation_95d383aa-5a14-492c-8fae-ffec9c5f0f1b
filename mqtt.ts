import mqtt, { MqttClient, IClientOptions } from 'mqtt'
import { EventEmitter } from 'events'
import { REDIS_KEYS, RedisKeyBuilder } from './redis'
import { QueueManager } from './queue'

// MQTT Topic structure for GoMama
export const MQTT_TOPICS = {
  // Listings
  LISTING_STATUS: 'gomama/listings/status',           // Pattern: gomama/listings/status/{listing_id}
  LISTING_AVAILABILITY: 'gomama/listings/availability', // Pattern: gomama/listings/availability/{listing_id}
  
  // Sessions  
  SESSION_CREATED: 'gomama/sessions/created',          // Pattern: gomama/sessions/created/{session_id}
  SESSION_UPDATED: 'gomama/sessions/updated',          // Pattern: gomama/sessions/updated/{session_id}
  SESSION_ENDED: 'gomama/sessions/ended',              // Pattern: gomama/sessions/ended/{session_id}
  SESSION_CLEANUP: 'gomama/sessions/cleanup',          // Pattern: gomama/sessions/cleanup/{session_id}
  
  // Users & Notifications
  USER_NOTIFICATION: 'gomama/users/notifications',     // Pattern: gomama/users/notifications/{user_id}
  USER_SESSION_UPDATE: 'gomama/users/sessions',        // Pattern: gomama/users/sessions/{user_id}
  
  // System
  SYSTEM_HEALTH: 'gomama/system/health',
  SYSTEM_METRICS: 'gomama/system/metrics',
  
  // Internal (realtime service only)
  INTERNAL_FIREBASE_UPDATE: 'gomama/internal/firebase', // Firebase → Realtime
  INTERNAL_REDIS_EVENT: 'gomama/internal/redis',       // Redis events → Realtime
} as const

// MQTT Topic builders
export const MQTTTopicBuilder = {
  // Listing topics
  listingStatus: (listingId: string) => `${MQTT_TOPICS.LISTING_STATUS}/${listingId}`,
  listingAvailability: (listingId: string) => `${MQTT_TOPICS.LISTING_AVAILABILITY}/${listingId}`,
  
  // Session topics
  sessionCreated: (sessionId: string) => `${MQTT_TOPICS.SESSION_CREATED}/${sessionId}`,
  sessionUpdated: (sessionId: string) => `${MQTT_TOPICS.SESSION_UPDATED}/${sessionId}`,
  sessionEnded: (sessionId: string) => `${MQTT_TOPICS.SESSION_ENDED}/${sessionId}`,
  sessionCleanup: (sessionId: string) => `${MQTT_TOPICS.SESSION_CLEANUP}/${sessionId}`,
  
  // User topics
  userNotification: (userId: string) => `${MQTT_TOPICS.USER_NOTIFICATION}/${userId}`,
  userSessionUpdate: (userId: string) => `${MQTT_TOPICS.USER_SESSION_UPDATE}/${userId}`,
  
  // Internal topics
  firebaseUpdate: (docId: string) => `${MQTT_TOPICS.INTERNAL_FIREBASE_UPDATE}/${docId}`,
  redisEvent: (eventType: string) => `${MQTT_TOPICS.INTERNAL_REDIS_EVENT}/${eventType}`,
}

// MQTT Message types
export interface MQTTMessage {
  timestamp: number
  source: 'adonisjs' | 'realtime' | 'firebase' | 'system'
  type: string
  data: any
  messageId?: string
}

// MQTT Client wrapper class
export class GoMamaMQTTClient extends EventEmitter {
  private client: MqttClient | null = null
  private isConnected = false
  private reconnectAttempts = 0
  private maxReconnectAttempts = 10
  private reconnectInterval = 5000 // 5 seconds

  constructor(private options: IClientOptions) {
    super()
    this.setupClient()
  }

  private setupClient() {
    const clientOptions: IClientOptions = {
      ...this.options,
      clientId: this.options.clientId || `gomama_realtime_${Date.now()}`,
      clean: false, // Persistent session
      keepalive: 60,
      reconnectPeriod: this.reconnectInterval,
      connectTimeout: 30000,
      will: {
        topic: MQTT_TOPICS.SYSTEM_HEALTH,
        payload: JSON.stringify({
          timestamp: Date.now(),
          source: 'realtime',
          type: 'disconnect',
          data: { clientId: this.options.clientId, reason: 'unexpected' }
        }),
        qos: 1,
        retain: false
      }
    }

    this.client = mqtt.connect(clientOptions)
    this.setupEventHandlers()
  }

  private setupEventHandlers() {
    if (!this.client) return

    this.client.on('connect', () => {
      console.log('✅ MQTT Client connected to broker')
      this.isConnected = true
      this.reconnectAttempts = 0
      this.emit('connected')
      
      // Subscribe to topics that this realtime service should handle
      this.subscribeToRealtimeTopics()
      
      // Publish connection status
      this.publishSystemHealth('connected')
    })

    this.client.on('disconnect', () => {
      console.log('❌ MQTT Client disconnected from broker')
      this.isConnected = false
      this.emit('disconnected')
    })

    this.client.on('reconnect', () => {
      this.reconnectAttempts++
      console.log(`🔄 MQTT Client reconnecting... (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        console.error('❌ Max reconnection attempts reached. Stopping reconnection.')
        this.client?.end(true)
      }
    })

    this.client.on('error', (error) => {
      console.error('❌ MQTT Client error:', error)
      this.emit('error', error)
    })

    this.client.on('message', (topic, payload, packet) => {
      try {
        const message: MQTTMessage = JSON.parse(payload.toString())
        this.handleIncomingMessage(topic, message, packet)
      } catch (error) {
        console.error('❌ Error parsing MQTT message:', error)
        console.error('Topic:', topic, 'Payload:', payload.toString())
      }
    })
  }

  private async subscribeToRealtimeTopics() {
    if (!this.client || !this.isConnected) return

    // Subscribe to topics that the realtime service should handle
    const subscriptions = [
      // From AdonisJS - session events
      { topic: `${MQTT_TOPICS.SESSION_CREATED}/+`, qos: 1 },
      { topic: `${MQTT_TOPICS.SESSION_UPDATED}/+`, qos: 1 },
      { topic: `${MQTT_TOPICS.SESSION_ENDED}/+`, qos: 1 },
      
      // From AdonisJS - listing events  
      { topic: `${MQTT_TOPICS.LISTING_STATUS}/+`, qos: 1 },
      { topic: `${MQTT_TOPICS.LISTING_AVAILABILITY}/+`, qos: 1 },
      
      // Internal events
      { topic: `${MQTT_TOPICS.INTERNAL_FIREBASE_UPDATE}/+`, qos: 1 },
      { topic: `${MQTT_TOPICS.INTERNAL_REDIS_EVENT}/+`, qos: 1 },
      
      // System events
      { topic: MQTT_TOPICS.SYSTEM_HEALTH, qos: 0 },
    ]

    for (const sub of subscriptions) {
      this.client.subscribe(sub.topic, { qos: sub.qos }, (err) => {
        if (err) {
          console.error(`❌ Failed to subscribe to ${sub.topic}:`, err)
        } else {
          console.log(`✅ Subscribed to ${sub.topic}`)
        }
      })
    }
  }

  private handleIncomingMessage(topic: string, message: MQTTMessage, packet: any) {
    console.log(`📨 Received MQTT message on ${topic}:`, message.type)

    // Route messages based on topic patterns
    if (topic.startsWith(MQTT_TOPICS.SESSION_CREATED)) {
      this.handleSessionCreated(topic, message)
    } else if (topic.startsWith(MQTT_TOPICS.SESSION_UPDATED)) {
      this.handleSessionUpdated(topic, message)
    } else if (topic.startsWith(MQTT_TOPICS.SESSION_ENDED)) {
      this.handleSessionEnded(topic, message)
    } else if (topic.startsWith(MQTT_TOPICS.LISTING_STATUS)) {
      this.handleListingStatusUpdate(topic, message)
    } else if (topic.startsWith(MQTT_TOPICS.INTERNAL_FIREBASE_UPDATE)) {
      this.handleFirebaseUpdate(topic, message)
    } else if (topic.startsWith(MQTT_TOPICS.INTERNAL_REDIS_EVENT)) {
      this.handleRedisEvent(topic, message)
    } else {
      console.log(`ℹ️ Unhandled MQTT topic: ${topic}`)
    }

    // Emit event for other parts of the application
    this.emit('message', { topic, message, packet })
  }

  // Message handlers
  private handleSessionCreated(topic: string, message: MQTTMessage) {
    const sessionId = topic.split('/').pop()
    console.log(`🆕 Session created: ${sessionId}`)
    
    // Forward to Flutter clients via user-specific topic
    if (message.data.userId) {
      this.publishUserSessionUpdate(message.data.userId, {
        type: 'session_created',
        sessionId,
        data: message.data
      })
    }
  }

  private handleSessionUpdated(topic: string, message: MQTTMessage) {
    const sessionId = topic.split('/').pop()
    console.log(`🔄 Session updated: ${sessionId}`)
    
    // Forward to Flutter clients
    if (message.data.userId) {
      this.publishUserSessionUpdate(message.data.userId, {
        type: 'session_updated',
        sessionId,
        data: message.data
      })
    }
  }

  private handleSessionEnded(topic: string, message: MQTTMessage) {
    const sessionId = topic.split('/').pop()
    console.log(`🔚 Session ended: ${sessionId}`)
    
    // Trigger cleanup job
    QueueManager.addSessionCleanup({
      sessionId: sessionId!,
      userId: message.data.userId,
      reason: 'ended'
    })
    
    // Forward to Flutter clients
    if (message.data.userId) {
      this.publishUserSessionUpdate(message.data.userId, {
        type: 'session_ended',
        sessionId,
        data: message.data
      })
    }
  }

  private handleListingStatusUpdate(topic: string, message: MQTTMessage) {
    const listingId = topic.split('/').pop()
    console.log(`🏠 Listing status updated: ${listingId} -> ${message.data.status}`)
    
    // This will be forwarded to Flutter clients who are subscribed to this listing
    // The MQTT broker handles the fan-out to subscribed clients
  }

  private handleFirebaseUpdate(topic: string, message: MQTTMessage) {
    console.log(`🔥 Firebase update received:`, message.type)
    // Handle Firebase-specific updates
  }

  private handleRedisEvent(topic: string, message: MQTTMessage) {
    console.log(`📊 Redis event received:`, message.type)
    // Handle Redis keyspace events
  }

  // Publishing methods
  async publish(topic: string, message: MQTTMessage, options: { qos?: 0 | 1 | 2, retain?: boolean } = {}) {
    if (!this.client || !this.isConnected) {
      console.error('❌ Cannot publish: MQTT client not connected')
      return false
    }

    const payload = JSON.stringify(message)
    const publishOptions = {
      qos: options.qos || 1,
      retain: options.retain || false
    }

    return new Promise<boolean>((resolve) => {
      this.client!.publish(topic, payload, publishOptions, (err) => {
        if (err) {
          console.error(`❌ Failed to publish to ${topic}:`, err)
          resolve(false)
        } else {
          console.log(`✅ Published to ${topic}`)
          resolve(true)
        }
      })
    })
  }

  // Convenience publishing methods
  async publishUserNotification(userId: string, notification: any) {
    return this.publish(MQTTTopicBuilder.userNotification(userId), {
      timestamp: Date.now(),
      source: 'realtime',
      type: 'notification',
      data: notification
    }, { qos: 1, retain: false })
  }

  async publishUserSessionUpdate(userId: string, sessionUpdate: any) {
    return this.publish(MQTTTopicBuilder.userSessionUpdate(userId), {
      timestamp: Date.now(),
      source: 'realtime',
      type: 'session_update',
      data: sessionUpdate
    }, { qos: 1, retain: true }) // Retain for offline clients
  }

  async publishListingStatus(listingId: string, status: string) {
    return this.publish(MQTTTopicBuilder.listingStatus(listingId), {
      timestamp: Date.now(),
      source: 'realtime',
      type: 'status_update',
      data: { listingId, status }
    }, { qos: 1, retain: true }) // Retain for offline clients
  }

  async publishSystemHealth(status: 'connected' | 'disconnected' | 'error', data?: any) {
    return this.publish(MQTT_TOPICS.SYSTEM_HEALTH, {
      timestamp: Date.now(),
      source: 'realtime',
      type: 'health_status',
      data: { status, clientId: this.options.clientId, ...data }
    }, { qos: 0, retain: false })
  }

  // Connection management
  isClientConnected(): boolean {
    return this.isConnected
  }

  async disconnect(): Promise<void> {
    if (this.client) {
      await this.publishSystemHealth('disconnected', { reason: 'graceful_shutdown' })
      this.client.end(true)
      this.isConnected = false
    }
  }
}

// Export singleton instance
let mqttClient: GoMamaMQTTClient | null = null

export function setupMQTT(): GoMamaMQTTClient {
  if (mqttClient) {
    return mqttClient
  }

  const brokerUrl = `mqtt://${process.env.MQTT_BROKER_HOST || 'localhost'}:${process.env.MQTT_BROKER_PORT || 1883}`
  
  const options: IClientOptions = {
    clientId: process.env.MQTT_CLIENT_ID || `gomama_realtime_${Date.now()}`,
    username: process.env.MQTT_USERNAME,
    password: process.env.MQTT_PASSWORD,
    servers: [{ host: process.env.MQTT_BROKER_HOST || 'localhost', port: Number(process.env.MQTT_BROKER_PORT || 1883) }]
  }

  mqttClient = new GoMamaMQTTClient(options)
  
  console.log(`🚀 Setting up MQTT client connecting to: ${brokerUrl}`)
  
  return mqttClient
}

export function getMQTTClient(): GoMamaMQTTClient | null {
  return mqttClient
}
