# EMQX Configuration for GoMama Realtime
# This file contains the main configuration for the EMQX MQTT broker

# Node configuration
node {
  name = "gomama_emqx@127.0.0.1"
  cookie = "gomama_emqx_cookie_2024"
  data_dir = "/opt/emqx/data"
}

# Cluster configuration (single node for now)
cluster {
  name = gomama_cluster
  discovery_strategy = manual
}

# MQTT Protocol Settings
mqtt {
  # Maximum client ID length
  max_clientid_len = 65535
  
  # Maximum packet size (1MB)
  max_packet_size = "1MB"
  
  # Maximum QoS level allowed
  max_qos_allowed = 2
  
  # Enable retained messages
  retain_available = true
  
  # Enable wildcard subscriptions
  wildcard_subscription = true
  
  # Enable shared subscriptions
  shared_subscription = true
  
  # Session expiry interval (2 hours)
  session_expiry_interval = "2h"
  
  # Maximum subscriptions per client
  max_subscriptions = 1000
  
  # Keep alive settings
  keepalive_multiplier = 1.25
  max_keepalive = 65535
  
  # Message settings
  max_mqueue_len = 1000
  mqueue_priorities = disabled
  mqueue_default_priority = lowest
  mqueue_store_qos0 = true
}

# Listeners
listeners.tcp.default {
  bind = "0.0.0.0:1883"
  max_connections = 10000
  max_conn_rate = 1000
  
  # Authentication
  enable_authn = true
  
  # Access control
  access_rules = ["allow all"]
}

listeners.ssl.default {
  bind = "0.0.0.0:8883"
  max_connections = 5000
  max_conn_rate = 500
  
  # SSL settings (configure certificates in production)
  ssl_options {
    keyfile = "/opt/emqx/etc/certs/key.pem"
    certfile = "/opt/emqx/etc/certs/cert.pem"
    cacertfile = "/opt/emqx/etc/certs/cacert.pem"
    verify = verify_none
  }
  
  enable_authn = true
  access_rules = ["allow all"]
}

listeners.ws.default {
  bind = "0.0.0.0:8083"
  max_connections = 5000
  max_conn_rate = 500
  websocket.mqtt_path = "/mqtt"
  
  enable_authn = true
  access_rules = ["allow all"]
}

listeners.wss.default {
  bind = "0.0.0.0:8084"
  max_connections = 2500
  max_conn_rate = 250
  websocket.mqtt_path = "/mqtt"
  
  # SSL settings
  ssl_options {
    keyfile = "/opt/emqx/etc/certs/key.pem"
    certfile = "/opt/emqx/etc/certs/cert.pem"
    cacertfile = "/opt/emqx/etc/certs/cacert.pem"
    verify = verify_none
  }
  
  enable_authn = true
  access_rules = ["allow all"]
}

# Authentication - Built-in database
authentication = [
  {
    mechanism = password_based
    backend = built_in_database
    user_id_type = username
    password_hash_algorithm {
      name = bcrypt
      salt_rounds = 10
    }
  }
]

# Authorization - Built-in database  
authorization {
  sources = [
    {
      type = built_in_database
      enable = true
    }
  ]
  
  # Default permissions
  no_match = deny
  deny_action = ignore
  cache {
    enable = true
    max_size = 32
    ttl = "1m"
  }
}

# Message retention
retainer {
  enable = true
  
  # Message expiry (1 hour)
  msg_expiry_interval = "1h"
  
  # Maximum payload size
  max_payload_size = "1MB"
  
  # Storage backend
  backend {
    type = built_in_database
    storage_type = disc
    max_retained_messages = 100000
  }
}

# Logging
log {
  console {
    enable = true
    level = info
    formatter = text
    time_offset = system
  }
  
  file {
    enable = true
    level = info
    file = "/opt/emqx/log/emqx.log"
    formatter = text
    rotation_size = "50MB"
    rotation_count = 10
  }
}

# Dashboard
dashboard {
  listeners.http {
    bind = "0.0.0.0:18083"
    max_connections = 100
    max_conn_rate = 50
  }
  
  default_username = "admin"
  default_password = "gomama2024!"
  
  # JWT token settings
  token_expired_time = "60m"
  cors = true
}

# API Keys (for management API)
api_key {
  bootstrap_file = "/opt/emqx/etc/api_keys_bootstrap.txt"
}

# Plugins
plugins {
  install_dir = "/opt/emqx/plugins"
  etc_dir = "/opt/emqx/etc/plugins"
}

# System tuning
sysmon {
  vm {
    process_check_interval = "30s"
    process_high_watermark = "80%"
    process_low_watermark = "60%"
  }
  
  os {
    cpu_check_interval = "60s"
    cpu_high_watermark = "80%"
    cpu_low_watermark = "60%"
    
    mem_check_interval = "60s"
    sysmem_high_watermark = "70%"
    procmem_high_watermark = "5%"
  }
}
