# EMQX HTTP Authentication Plugin Configuration
# This plugin will authenticate MQTT clients against our Node.js authentication service

# Enable the plugin
auth.http.auth_req = http://gomama_realtime:9001/mqtt/auth
auth.http.auth_req.method = post
auth.http.auth_req.headers.content-type = application/json
auth.http.auth_req.params = clientid=%c,username=%u,password=%P,peerhost=%a,protocol=%r

# Super user request
auth.http.super_req = http://gomama_realtime:9001/mqtt/superuser
auth.http.super_req.method = post
auth.http.super_req.headers.content-type = application/json
auth.http.super_req.params = clientid=%c,username=%u,peerhost=%a,protocol=%r

# ACL (Access Control List) request
auth.http.acl_req = http://gomama_realtime:9001/mqtt/acl
auth.http.acl_req.method = post
auth.http.acl_req.headers.content-type = application/json
auth.http.acl_req.params = access=%A,username=%u,clientid=%c,ipaddr=%a,topic=%t,mountpoint=%m,protocol=%r

# HTTP request timeout
auth.http.timeout = 5s

# HTTP request retry times
auth.http.retry_times = 3

# HTTP request retry interval
auth.http.retry_interval = 1s

# Enable SSL for HTTP requests (set to off for development)
auth.http.ssl = off
