# MQTT Broker Setup with EMQX
version: '3.8'

services:
  # EMQX MQTT Broker
  emqx:
    image: emqx/emqx:5.4.1
    container_name: gomama_emqx
    restart: unless-stopped
    ports:
      - "1883:1883"    # MQTT port
      - "8883:8883"    # MQTT SSL port
      - "8083:8083"    # MQTT over WebSocket
      - "8084:8084"    # MQTT over WebSocket SSL
      - "18083:18083"  # Dashboard
    environment:
      # Basic configuration
      EMQX_NAME: gomama_emqx
      EMQX_HOST: 127.0.0.1
      
      # Authentication
      EMQX_AUTH__MNESIA__PASSWORD_HASH: bcrypt
      
      # Logging
      EMQX_LOG__CONSOLE__LEVEL: info
      EMQX_LOG__FILE__LEVEL: info
      
      # Limits and performance
      EMQX_MQTT__MAX_CLIENTID_LEN: 65535
      EMQX_MQTT__MAX_PACKET_SIZE: 1MB
      EMQX_MQTT__MAX_QOS_ALLOWED: 2
      EMQX_MQTT__RETAIN_AVAILABLE: true
      EMQX_MQTT__WILDCARD_SUBSCRIPTION: true
      EMQX_MQTT__SHARED_SUBSCRIPTION: true
      
      # Session settings
      EMQX_MQTT__SESSION_EXPIRY_INTERVAL: 7200s  # 2 hours
      EMQX_MQTT__MAX_SUBSCRIPTIONS: 1000
      
      # Message persistence
      EMQX_RETAINER__ENABLE: true
      EMQX_RETAINER__MSG_EXPIRY_INTERVAL: 3600s  # 1 hour
      EMQX_RETAINER__MAX_PAYLOAD_SIZE: 1MB
      
      # Dashboard credentials (change in production!)
      EMQX_DASHBOARD__DEFAULT_USERNAME: admin
      EMQX_DASHBOARD__DEFAULT_PASSWORD: gomama2024!
      
    volumes:
      - ./emqx/data:/opt/emqx/data
      - ./emqx/log:/opt/emqx/log
      - ./emqx/etc:/opt/emqx/etc
    networks:
      - gomama_mqtt
    healthcheck:
      test: ["CMD", "/opt/emqx/bin/emqx", "ctl", "status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Redis (keep for data storage)
  redis:
    image: redis:7-alpine
    container_name: gomama_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - ./redis/data:/data
    networks:
      - gomama_mqtt
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Realtime service (updated for MQTT)
  gomama_realtime:
    container_name: gomama_realtime
    build: .
    restart: unless-stopped
    env_file:
      - .env
    environment:
      # MQTT Configuration
      MQTT_BROKER_HOST: emqx
      MQTT_BROKER_PORT: 1883
      MQTT_CLIENT_ID: gomama_realtime_${HOSTNAME:-server}
      MQTT_USERNAME: ${MQTT_USERNAME:-realtime_client}
      MQTT_PASSWORD: ${MQTT_PASSWORD:-realtime_pass}
      
      # Redis Configuration  
      REDIS_HOST: redis
      REDIS_PORT: 6379
      
    ports:
      - "9001:9001"  # Keep HTTP metrics endpoint
    depends_on:
      emqx:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - gomama_mqtt
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '0.50'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9001/metrics"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  gomama_mqtt:
    driver: bridge
    name: gomama_mqtt

volumes:
  emqx_data:
  emqx_log:
  emqx_etc:
  redis_data:
