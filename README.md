# `gomama_realtime` - Real-time Services

## Overview

This repository contains the real-time service for the Gomama platform, built with **Express.js and MQTT (TypeScript)**. Its primary role is to bridge communication between backend services and mobile clients using MQTT for reliable real-time messaging, replacing the previous WebSocket implementation.

## Key Responsibilities

-   **MQTT Bridge:** Connects to MQTT broker and bridges messages between Redis pub/sub and MQTT topics.
-   **HTTP API Server:** Provides MQTT authentication endpoints and system metrics via Express.js.
-   **Firebase Integration:** Listens for real-time updates from Firestore and publishes them to MQTT topics.
-   **Redis Integration:**
    -   Subscribes to Redis channels to receive messages from `gomama_adonis`.
    -   Bridges Redis messages to MQTT topics for client consumption.
-   **Client Communication:** Publishes real-time updates to MQTT topics for Flutter app consumption, including booking status changes, notifications, and location tracking.
-   **Authentication:** Validates MQTT client credentials and manages topic-based access control.

## Tech Stack

-   **Core:** Express.js (TypeScript) for HTTP API
-   **Messaging:** MQTT (with EMQX broker) for real-time communication
-   **Real-time Database:** Google Firestore
-   **Caching/Pub-Sub:** Redis
-   **Queue Management:** BullMQ for background job processing

## Getting Started

1.  **Install Dependencies:**
    ```bash
    npm install
    ```
2.  **Setup Environment Variables:**
    -   Create a `.env` file.
    -   Provide connection details for Redis, Firebase (service account), and MQTT broker.
3.  **Start MQTT Infrastructure:**
    ```bash
    # Start MQTT broker and related services
    ./scripts/start-mqtt.sh
    ```
4.  **Start the Development Server:**
    ```bash
    npm run dev
    ```

The HTTP server will be running on the port specified in your `.env` file (e.g., `9001`), providing MQTT authentication endpoints and system metrics.

## MQTT Architecture

For detailed information about the MQTT implementation, see:
- [MQTT Migration Guide](MQTT_MIGRATION_GUIDE.md)
- [MQTT README](README_MQTT.md)
- [Flutter MQTT Integration](flutter-mqtt-integration.md)
- [AdonisJS MQTT Integration](adonisjs-mqtt-integration.md)