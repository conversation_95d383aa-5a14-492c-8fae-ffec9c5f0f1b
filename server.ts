import * as uWS from 'uWebSockets.js'
import dotenv from 'dotenv'
import { setupWebSocket } from './websocket'
import { setupRedis } from './redis'
import { setupFirebase } from './firebase'
import { setupRedisKeyEvents } from './events'
import { setupMonitoring, getSystemMetrics } from './monitoring'
import { workers } from './workers'
import { QueueManager } from './queue'
import { setupMQTT, getMQTTClient } from './mqtt'
import { setupRedisMQTTBridge, getRedisMQTTBridge } from './redis-mqtt-bridge'
import { validateMQTTCredentials, checkTopicPermission } from './mqtt-auth'

dotenv.config()
const port = Number(process.env.PORT || 9001)
const redisUrl = `redis://${process.env.REDIS_HOST}:${process.env.REDIS_PORT}`

async function startServer() {
  // Initialize monitoring
  setupMonitoring()

  const { redisClient, redisSubscriber } = await setupRedis(redisUrl)

  // Setup MQTT client
  const mqttClient = setupMQTT()

  // Wait for MQTT connection before proceeding
  await new Promise<void>((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error('MQTT connection timeout'))
    }, 30000) // 30 second timeout

    mqttClient.once('connected', () => {
      clearTimeout(timeout)
      resolve()
    })

    mqttClient.once('error', (error) => {
      clearTimeout(timeout)
      reject(error)
    })
  })

  // Setup Redis-MQTT Bridge
  const bridge = setupRedisMQTTBridge(redisClient, mqttClient)

  setupFirebase(redisClient)
  setupRedisKeyEvents(redisSubscriber)

  // Start BullMQ workers
  console.log('Starting BullMQ workers...')
  Object.values(workers).forEach((worker) => {
    console.log(`Started worker: ${worker.name}`)
  })

  const app = uWS
    .App()
    .ws('/*', setupWebSocket(redisClient))
    // ------------------------
    // gomama_rt broadcast
    // ------------------------

    // Add a metrics endpoint
    .get('/metrics', async (res, req) => {
      const metricsApiKey = process.env.METRICS_API_KEY
      if (!metricsApiKey || req.getHeader('authorization') !== `Bearer ${metricsApiKey}`) {
        res.writeStatus('401 Unauthorized').end('Unauthorized')
        return
      }

      const systemMetrics = getSystemMetrics()
      const queueStats = await QueueManager.getQueueStats()
      const mqttClient = getMQTTClient()
      const bridge = getRedisMQTTBridge()

      const metrics = {
        ...systemMetrics,
        queues: queueStats,
        mqtt: {
          connected: mqttClient?.isClientConnected() || false,
          clientId: process.env.MQTT_CLIENT_ID
        },
        bridge: bridge?.getStats() || { enabled: false },
        timestamp: Date.now()
      }

      res.cork(() => {
        res.writeHeader('Content-Type', 'application/json')
        res.writeStatus('200 OK').end(JSON.stringify(metrics, null, 2))
      })
    })
    // MQTT Authentication endpoints
    .post('/mqtt/auth', (res, req) => {
      readJson(
        res,
        (body: { clientid: string; username: string; password: string; peerhost: string }) => {
          const { username, password } = body
          const result = validateMQTTCredentials(username, password)

          if (result.valid) {
            res.writeStatus('200 OK').end('allow')
          } else {
            res.writeStatus('401 Unauthorized').end('deny')
          }
        },
        () => {
          res.writeStatus('400 Bad Request').end('deny')
        }
      )
    })
    .post('/mqtt/superuser', (res, req) => {
      readJson(
        res,
        (body: { clientid: string; username: string; peerhost: string }) => {
          const { username } = body
          // Only realtime and admin clients are superusers
          const isSuperuser = username.startsWith('admin_') || username.startsWith('realtime_')
          res.writeStatus('200 OK').end(isSuperuser ? 'allow' : 'deny')
        },
        () => {
          res.writeStatus('400 Bad Request').end('deny')
        }
      )
    })
    .post('/mqtt/acl', (res, req) => {
      readJson(
        res,
        (body: { access: string; username: string; clientid: string; topic: string }) => {
          const { access, username, topic } = body

          // Extract token from username (format: clienttype_userid)
          const parts = username.split('_')
          if (parts.length < 2) {
            res.writeStatus('401 Unauthorized').end('deny')
            return
          }

          // For now, allow all authenticated users (token validation was done in auth step)
          // In production, implement proper topic-based ACL using checkTopicPermission
          const action = access === '1' ? 'subscribe' : 'publish'
          res.writeStatus('200 OK').end('allow')
        },
        () => {
          res.writeStatus('400 Bad Request').end('deny')
        }
      )
    })
    .any('/*', (res, req) => {
      res.end('Nothing to see here!')
    })
    .listen(port, (token) => {
      if (token) {
        console.log('Listening to port ' + port)
      } else {
        console.log('Failed to listen to port ' + port)
      }
    })

  // Setup Redis subscriber with MQTT bridge
  redisSubscriber.on('pmessage', async (_: string, channel: string, message: string) => {
    console.log("Received from Redis channel '%s'", channel)

    // Publish to WebSocket (backward compatibility)
    app.publish(channel, JSON.stringify({ topic: channel, data: message }))

    // Bridge to MQTT for new clients
    const bridge = getRedisMQTTBridge()
    if (bridge) {
      await bridge.bridgeRedisToMQTT(channel, message)
    }
  })

  // Setup session checking
}

startServer().catch(console.error)

// ------------------------
// gomama_rt broadcast
// ------------------------
// .post("/broadcast", (res, req) => {
//   readJson(
//     res,
//     (body: { topic: string; data: string }) => {
//       const topic = body.topic
//       const data = body.data

//       if (topic && data) {
//         app.publish(topic, data)
//       }

//       res.writeStatus("200 OK").end("Message published")
//     },
//     () => {
//       /* Request was prematurely aborted or invalid or missing, stop reading */
//       console.log("Invalid JSON or no data at all!")
//       res.writeStatus("400 Bad Request").end("Invalid JSON")
//     }
//   )
// })

// util functions
function readJson(res: uWS.HttpResponse, cb: (json: any) => void, err: () => void): void {
  let buffer: Buffer | undefined

  /* Register data cb */
  res.onData((ab: ArrayBuffer, isLast: boolean) => {
    const chunk = Buffer.from(ab)
    if (isLast) {
      let json: any
      if (buffer) {
        try {
          json = JSON.parse(Buffer.concat([buffer, chunk]).toString())
        } catch (e) {
          /* res.close calls onAborted */
          res.close()
          return
        }
        cb(json)
      } else {
        try {
          json = JSON.parse(chunk.toString())
        } catch (e) {
          /* res.close calls onAborted */
          res.close()
          return
        }
        cb(json)
      }
    } else {
      if (buffer) {
        buffer = Buffer.concat([buffer, chunk])
      } else {
        buffer = chunk
      }
    }
  })

  /* Register error cb */
  res.onAborted(err)
}
