import jwt from 'jsonwebtoken'
import crypto from 'crypto'

/**
 * MQTT Authentication and Authorization Service
 * Handles JWT-based authentication and topic-based authorization for MQTT clients
 */

// JWT payload interface for MQTT clients
export interface MQTTJWTPayload {
  userId: string
  clientType: 'flutter' | 'adonisjs' | 'realtime' | 'admin'
  permissions: string[]
  deviceId?: string
  exp: number
  iat: number
}

// Topic permission patterns
export const TOPIC_PERMISSIONS = {
  // Read permissions
  READ_LISTINGS: 'listings:read',
  READ_SESSIONS: 'sessions:read',
  READ_USER_DATA: 'user:read',
  READ_SYSTEM: 'system:read',
  
  // Write permissions
  WRITE_LISTINGS: 'listings:write',
  WRITE_SESSIONS: 'sessions:write',
  WRITE_USER_DATA: 'user:write',
  WRITE_SYSTEM: 'system:write',
  
  // Admin permissions
  ADMIN_ALL: 'admin:all',
} as const

// Client type permission mappings
export const CLIENT_PERMISSIONS = {
  flutter: [
    TOPIC_PERMISSIONS.READ_LISTINGS,
    TOPIC_PERMISSIONS.READ_SESSIONS,
    TOPIC_PERMISSIONS.READ_USER_DATA,
  ],
  adonisjs: [
    TOPIC_PERMISSIONS.WRITE_LISTINGS,
    TOPIC_PERMISSIONS.WRITE_SESSIONS,
    TOPIC_PERMISSIONS.WRITE_USER_DATA,
    TOPIC_PERMISSIONS.READ_SYSTEM,
  ],
  realtime: [
    TOPIC_PERMISSIONS.READ_LISTINGS,
    TOPIC_PERMISSIONS.WRITE_LISTINGS,
    TOPIC_PERMISSIONS.READ_SESSIONS,
    TOPIC_PERMISSIONS.WRITE_SESSIONS,
    TOPIC_PERMISSIONS.READ_USER_DATA,
    TOPIC_PERMISSIONS.WRITE_USER_DATA,
    TOPIC_PERMISSIONS.READ_SYSTEM,
    TOPIC_PERMISSIONS.WRITE_SYSTEM,
  ],
  admin: [TOPIC_PERMISSIONS.ADMIN_ALL],
} as const

export class MQTTAuthService {
  private jwtSecret: string

  constructor(jwtSecret?: string) {
    this.jwtSecret = jwtSecret || process.env.JWT_SECRET || 'default_mqtt_secret'
  }

  /**
   * Generate JWT token for MQTT client
   */
  generateToken(payload: Omit<MQTTJWTPayload, 'iat' | 'exp'>): string {
    const now = Math.floor(Date.now() / 1000)
    const fullPayload: MQTTJWTPayload = {
      ...payload,
      iat: now,
      exp: now + (24 * 60 * 60), // 24 hours
    }

    return jwt.sign(fullPayload, this.jwtSecret)
  }

  /**
   * Verify JWT token
   */
  verifyToken(token: string): MQTTJWTPayload | null {
    try {
      const decoded = jwt.verify(token, this.jwtSecret) as MQTTJWTPayload
      return decoded
    } catch (error) {
      console.error('JWT verification failed:', error)
      return null
    }
  }

  /**
   * Check if client has permission for a specific topic and action
   */
  hasTopicPermission(
    payload: MQTTJWTPayload, 
    topic: string, 
    action: 'subscribe' | 'publish'
  ): boolean {
    // Admin has access to everything
    if (payload.permissions.includes(TOPIC_PERMISSIONS.ADMIN_ALL)) {
      return true
    }

    // Check topic-specific permissions
    const topicPattern = this.getTopicPattern(topic)
    const requiredPermission = this.getRequiredPermission(topicPattern, action)

    if (!requiredPermission) {
      return false
    }

    // Check if user has the required permission
    if (!payload.permissions.includes(requiredPermission)) {
      return false
    }

    // Additional checks for user-specific topics
    if (topicPattern.includes('{user_id}')) {
      return this.checkUserSpecificAccess(payload, topic)
    }

    return true
  }

  /**
   * Get topic pattern from actual topic
   */
  private getTopicPattern(topic: string): string {
    // Convert actual topics to patterns
    const patterns = [
      { regex: /^gomama\/listings\/status\/[^\/]+$/, pattern: 'gomama/listings/status/{listing_id}' },
      { regex: /^gomama\/listings\/availability\/[^\/]+$/, pattern: 'gomama/listings/availability/{listing_id}' },
      { regex: /^gomama\/sessions\/created\/[^\/]+$/, pattern: 'gomama/sessions/created/{session_id}' },
      { regex: /^gomama\/sessions\/updated\/[^\/]+$/, pattern: 'gomama/sessions/updated/{session_id}' },
      { regex: /^gomama\/sessions\/ended\/[^\/]+$/, pattern: 'gomama/sessions/ended/{session_id}' },
      { regex: /^gomama\/sessions\/cleanup\/[^\/]+$/, pattern: 'gomama/sessions/cleanup/{session_id}' },
      { regex: /^gomama\/users\/notifications\/[^\/]+$/, pattern: 'gomama/users/notifications/{user_id}' },
      { regex: /^gomama\/users\/sessions\/[^\/]+$/, pattern: 'gomama/users/sessions/{user_id}' },
      { regex: /^gomama\/system\/health$/, pattern: 'gomama/system/health' },
      { regex: /^gomama\/system\/metrics$/, pattern: 'gomama/system/metrics' },
    ]

    for (const { regex, pattern } of patterns) {
      if (regex.test(topic)) {
        return pattern
      }
    }

    return topic // Return original if no pattern matches
  }

  /**
   * Get required permission for topic pattern and action
   */
  private getRequiredPermission(topicPattern: string, action: 'subscribe' | 'publish'): string | null {
    const isWrite = action === 'publish'

    if (topicPattern.startsWith('gomama/listings/')) {
      return isWrite ? TOPIC_PERMISSIONS.WRITE_LISTINGS : TOPIC_PERMISSIONS.READ_LISTINGS
    }

    if (topicPattern.startsWith('gomama/sessions/')) {
      return isWrite ? TOPIC_PERMISSIONS.WRITE_SESSIONS : TOPIC_PERMISSIONS.READ_SESSIONS
    }

    if (topicPattern.startsWith('gomama/users/')) {
      return isWrite ? TOPIC_PERMISSIONS.WRITE_USER_DATA : TOPIC_PERMISSIONS.READ_USER_DATA
    }

    if (topicPattern.startsWith('gomama/system/')) {
      return isWrite ? TOPIC_PERMISSIONS.WRITE_SYSTEM : TOPIC_PERMISSIONS.READ_SYSTEM
    }

    return null
  }

  /**
   * Check user-specific topic access
   */
  private checkUserSpecificAccess(payload: MQTTJWTPayload, topic: string): boolean {
    // Extract user ID from topic
    const userIdMatch = topic.match(/\/users\/[^\/]+\/([^\/]+)/)
    if (!userIdMatch) {
      return false
    }

    const topicUserId = userIdMatch[1]
    
    // Users can only access their own topics (unless admin)
    return payload.userId === topicUserId || payload.permissions.includes(TOPIC_PERMISSIONS.ADMIN_ALL)
  }

  /**
   * Generate client credentials for different client types
   */
  generateClientCredentials(
    userId: string, 
    clientType: keyof typeof CLIENT_PERMISSIONS,
    deviceId?: string
  ): { username: string, password: string, token: string } {
    const permissions = [...CLIENT_PERMISSIONS[clientType]]
    
    const token = this.generateToken({
      userId,
      clientType,
      permissions,
      deviceId,
    })

    // Use token as password for MQTT authentication
    const username = `${clientType}_${userId}`
    const password = token

    return { username, password, token }
  }

  /**
   * Validate MQTT client credentials
   */
  validateCredentials(username: string, password: string): {
    valid: boolean
    payload?: MQTTJWTPayload
    error?: string
  } {
    try {
      // Password should be the JWT token
      const payload = this.verifyToken(password)
      
      if (!payload) {
        return { valid: false, error: 'Invalid token' }
      }

      // Verify username matches token
      const expectedUsername = `${payload.clientType}_${payload.userId}`
      if (username !== expectedUsername) {
        return { valid: false, error: 'Username mismatch' }
      }

      return { valid: true, payload }
    } catch (error) {
      return { valid: false, error: 'Authentication failed' }
    }
  }

  /**
   * Generate API key for management operations
   */
  generateApiKey(): { apiKey: string, secret: string } {
    const apiKey = `gomama_${crypto.randomBytes(16).toString('hex')}`
    const secret = crypto.randomBytes(32).toString('hex')
    
    return { apiKey, secret }
  }
}

// Export singleton instance
let authService: MQTTAuthService | null = null

export function getMQTTAuthService(): MQTTAuthService {
  if (!authService) {
    authService = new MQTTAuthService()
  }
  return authService
}

// Helper functions for common operations
export function generateFlutterCredentials(userId: string, deviceId: string) {
  return getMQTTAuthService().generateClientCredentials(userId, 'flutter', deviceId)
}

export function generateAdonisJSCredentials(userId: string = 'adonisjs_server') {
  return getMQTTAuthService().generateClientCredentials(userId, 'adonisjs')
}

export function generateRealtimeCredentials(userId: string = 'realtime_server') {
  return getMQTTAuthService().generateClientCredentials(userId, 'realtime')
}

export function validateMQTTCredentials(username: string, password: string) {
  return getMQTTAuthService().validateCredentials(username, password)
}

export function checkTopicPermission(
  token: string, 
  topic: string, 
  action: 'subscribe' | 'publish'
): boolean {
  const authService = getMQTTAuthService()
  const payload = authService.verifyToken(token)
  
  if (!payload) {
    return false
  }

  return authService.hasTopicPermission(payload, topic, action)
}
