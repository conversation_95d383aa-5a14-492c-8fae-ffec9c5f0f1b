import { Redis } from 'ioredis'
import { GoMamaMQTTClient, MQTTTopicBuilder } from './mqtt'
import { REDIS_KEYS } from './redis'

/**
 * Redis-MQTT Bridge Service
 * Provides seamless integration between Redis pub/sub and MQTT topics
 * Enables backward compatibility during migration
 */
export class RedisMQTTBridge {
  private redisClient: Redis
  private mqttClient: GoMamaMQTTClient
  private isEnabled: boolean = true

  constructor(redisClient: Redis, mqttClient: GoMamaMQTTClient) {
    this.redisClient = redisClient
    this.mqttClient = mqttClient
  }

  /**
   * Enable or disable the bridge
   */
  setEnabled(enabled: boolean) {
    this.isEnabled = enabled
    console.log(`🌉 Redis-MQTT Bridge ${enabled ? 'enabled' : 'disabled'}`)
  }

  /**
   * Bridge Redis pub/sub message to MQTT topic
   */
  async bridgeRedisToMQTT(redisChannel: string, message: string): Promise<boolean> {
    if (!this.isEnabled || !this.mqttClient.isClientConnected()) {
      return false
    }

    try {
      const mapping = this.getChannelMapping(redisChannel, message)
      if (!mapping) {
        return false
      }

      await this.mqttClient.publish(mapping.topic, mapping.message, mapping.options)
      console.log(`🌉 Bridged: ${redisChannel} → ${mapping.topic}`)
      return true
    } catch (error) {
      console.error(`❌ Bridge error for ${redisChannel}:`, error)
      return false
    }
  }

  /**
   * Bridge MQTT message to Redis pub/sub (for backward compatibility)
   */
  async bridgeMQTTToRedis(mqttTopic: string, mqttMessage: any): Promise<boolean> {
    if (!this.isEnabled) {
      return false
    }

    try {
      const redisChannel = this.getMQTTToRedisMapping(mqttTopic)
      if (!redisChannel) {
        return false
      }

      const redisMessage = JSON.stringify(mqttMessage.data)
      await this.redisClient.publish(redisChannel, redisMessage)
      console.log(`🌉 Bridged: ${mqttTopic} → ${redisChannel}`)
      return true
    } catch (error) {
      console.error(`❌ Bridge error for ${mqttTopic}:`, error)
      return false
    }
  }

  /**
   * Map Redis channels to MQTT topics
   */
  private getChannelMapping(
    redisChannel: string,
    message: string
  ): {
    topic: string
    message: any
    options: { qos: 0 | 1 | 2; retain?: boolean }
  } | null {
    const baseMessage = {
      timestamp: Date.now(),
      source: 'redis_bridge',
      data: this.parseMessage(message)
    }

    // Listing status updates
    if (redisChannel.startsWith('gomama:channels:listing_status:')) {
      const listingId = redisChannel.split(':')[3]
      return {
        topic: MQTTTopicBuilder.listingStatus(listingId),
        message: {
          ...baseMessage,
          type: 'listing_status_update',
          data: { listingId, status: message }
        },
        options: { qos: 1, retain: true }
      }
    }

    // Session updates
    if (redisChannel.startsWith('gomama:channels:session_update:')) {
      const sessionId = redisChannel.split(':')[3]
      return {
        topic: MQTTTopicBuilder.sessionUpdated(sessionId),
        message: {
          ...baseMessage,
          type: 'session_update',
          data: { sessionId, ...this.parseMessage(message) }
        },
        options: { qos: 1, retain: false }
      }
    }

    // User notifications
    if (redisChannel.startsWith('gomama:channels:user_notification:')) {
      const userId = redisChannel.split(':')[3]
      return {
        topic: MQTTTopicBuilder.userNotification(userId),
        message: {
          ...baseMessage,
          type: 'user_notification',
          data: this.parseMessage(message)
        },
        options: { qos: 1, retain: false }
      }
    }

    // Session cleanup
    if (redisChannel === 'gomama:session:cleanup') {
      const sessionData = this.parseMessage(message)
      if (sessionData.sessionId) {
        return {
          topic: MQTTTopicBuilder.sessionCleanup(sessionData.sessionId),
          message: {
            ...baseMessage,
            type: 'session_cleanup',
            data: sessionData
          },
          options: { qos: 1, retain: false }
        }
      }
    }

    // System health
    if (redisChannel === 'gomama:system:health') {
      return {
        topic: 'gomama/system/health',
        message: {
          ...baseMessage,
          type: 'system_health',
          data: this.parseMessage(message)
        },
        options: { qos: 0, retain: false }
      }
    }

    return null
  }

  /**
   * Map MQTT topics back to Redis channels (for backward compatibility)
   */
  private getMQTTToRedisMapping(mqttTopic: string): string | null {
    // Listing status: gomama/listings/status/{id} → gomama:channels:listing_status:{id}
    if (mqttTopic.startsWith('gomama/listings/status/')) {
      const listingId = mqttTopic.split('/')[3]
      return `gomama:channels:listing_status:${listingId}`
    }

    // Session updates: gomama/sessions/updated/{id} → gomama:channels:session_update:{id}
    if (mqttTopic.startsWith('gomama/sessions/updated/')) {
      const sessionId = mqttTopic.split('/')[3]
      return `gomama:channels:session_update:${sessionId}`
    }

    // User notifications: gomama/users/notifications/{id} → gomama:channels:user_notification:{id}
    if (mqttTopic.startsWith('gomama/users/notifications/')) {
      const userId = mqttTopic.split('/')[3]
      return `gomama:channels:user_notification:${userId}`
    }

    // System health
    if (mqttTopic === 'gomama/system/health') {
      return 'gomama:system:health'
    }

    return null
  }

  /**
   * Parse message string to object
   */
  private parseMessage(message: string): any {
    try {
      return JSON.parse(message)
    } catch {
      return message
    }
  }

  /**
   * Publish data to both Redis and MQTT (dual publishing during migration)
   */
  async publishToBoth(
    redisChannel: string,
    mqttTopic: string,
    data: any,
    options: { qos?: 0 | 1 | 2; retain?: boolean } = {}
  ): Promise<{ redis: boolean; mqtt: boolean }> {
    const results = { redis: false, mqtt: false }

    // Publish to Redis
    try {
      const redisMessage = typeof data === 'string' ? data : JSON.stringify(data)
      await this.redisClient.publish(redisChannel, redisMessage)
      results.redis = true
    } catch (error) {
      console.error(`❌ Redis publish error:`, error)
    }

    // Publish to MQTT
    try {
      const mqttMessage = {
        timestamp: Date.now(),
        source: 'dual_publish',
        type: 'data_update',
        data
      }
      await this.mqttClient.publish(mqttTopic, mqttMessage, options)
      results.mqtt = true
    } catch (error) {
      console.error(`❌ MQTT publish error:`, error)
    }

    return results
  }

  /**
   * Helper methods for common publishing patterns
   */
  async publishListingStatus(listingId: string, status: string) {
    return this.publishToBoth(
      `gomama:channels:listing_status:${listingId}`,
      MQTTTopicBuilder.listingStatus(listingId),
      { listingId, status },
      { qos: 1, retain: true }
    )
  }

  async publishSessionUpdate(sessionId: string, sessionData: any) {
    return this.publishToBoth(
      `gomama:channels:session_update:${sessionId}`,
      MQTTTopicBuilder.sessionUpdated(sessionId),
      { sessionId, ...sessionData },
      { qos: 1, retain: false }
    )
  }

  async publishUserNotification(userId: string, notification: any) {
    return this.publishToBoth(
      `gomama:channels:user_notification:${userId}`,
      MQTTTopicBuilder.userNotification(userId),
      notification,
      { qos: 1, retain: false }
    )
  }

  /**
   * Get bridge statistics
   */
  getStats() {
    return {
      enabled: this.isEnabled,
      redisConnected: this.redisClient.status === 'ready',
      mqttConnected: this.mqttClient.isClientConnected(),
      timestamp: Date.now()
    }
  }
}

// Export singleton instance
let bridgeInstance: RedisMQTTBridge | null = null

export function setupRedisMQTTBridge(
  redisClient: Redis,
  mqttClient: GoMamaMQTTClient
): RedisMQTTBridge {
  if (!bridgeInstance) {
    bridgeInstance = new RedisMQTTBridge(redisClient, mqttClient)
    console.log('🌉 Redis-MQTT Bridge initialized')
  }
  return bridgeInstance
}

export function getRedisMQTTBridge(): RedisMQTTBridge | null {
  return bridgeInstance
}
