{"name": "gomama_realtime", "version": "1.0.0", "description": "", "main": "dist/server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc", "start": "node dist/server.js", "dev": "nodemon --watch ./src/**/*.ts --exec ts-node ./server.ts", "start:prod": "NODE_ENV=production node dist/server.js", "monitor": "node -e \"setInterval(() => console.log(require('os').loadavg()), 1000)\""}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bullmq": "^5.56.0", "dotenv": "^16.4.5", "firebase-admin": "^12.6.0", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "mqtt": "^5.3.4", "os": "^0.1.2", "uWebSockets.js": "https://github.com/uNetworking/uWebSockets.js.git#v20.48.0"}, "devDependencies": {"@types/dotenv": "^8.2.0", "@types/ioredis": "4.28.10", "@types/node": "^22.7.6", "nodemon": "^3.1.7", "ts-node": "^10.9.2", "typescript": "^5.6.3"}}